'use client';

import { useState } from 'react';
import { CategorySelector } from '@/components/ui/category-selector';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestCategoriesPage() {
  const [influencerCategories, setInfluencerCategories] = useState<number[]>([]);
  const [businessCategories, setBusinessCategories] = useState<number[]>([]);

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto max-w-4xl space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Test Kategorija</h1>
          <p className="text-muted-foreground">Testiranje CategorySelector komponente</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Influencer Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Influencer Kategorije</CardTitle>
              <CardDescription>
                Influencer može izabrati maksimalno 3 kategorije
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CategorySelector
                selectedCategories={influencerCategories}
                onCategoriesChange={setInfluencerCategories}
                maxCategories={3}
                placeholder="Izaberite vaše kategorije..."
              />
              
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Izabrane kategorije:</h4>
                <pre className="text-sm">
                  {JSON.stringify(influencerCategories, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Business Target Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Biznis Target Kategorije</CardTitle>
              <CardDescription>
                Biznis može izabrati maksimalno 3 target kategorije
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CategorySelector
                selectedCategories={businessCategories}
                onCategoriesChange={setBusinessCategories}
                maxCategories={3}
                placeholder="Izaberite target kategorije..."
              />
              
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Target kategorije:</h4>
                <pre className="text-sm">
                  {JSON.stringify(businessCategories, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Usage Example */}
        <Card>
          <CardHeader>
            <CardTitle>Kako koristiti</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium">Props:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li><code>selectedCategories</code> - niz ID-jeva izabranih kategorija</li>
                  <li><code>onCategoriesChange</code> - callback funkcija za promjene</li>
                  <li><code>maxCategories</code> - maksimalan broj kategorija (default: 3)</li>
                  <li><code>placeholder</code> - placeholder tekst</li>
                  <li><code>disabled</code> - da li je komponenta onemogućena</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium">Funkcionalnosti:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Multi-select sa maksimalno 3 kategorije</li>
                  <li>Pretraga kategorija po nazivu</li>
                  <li>Prikaz ikona i opisa za svaku kategoriju</li>
                  <li>Badge prikaz izabranih kategorija</li>
                  <li>Validacija maksimalnog broja</li>
                  <li>Loading state dok se učitavaju kategorije</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
