# Figma Prototip Specifikacija - InfluConnect

## 📋 Pregled

Ovaj dokument sadrži detaljnu specifikaciju za kreiranje interaktivnog prototipa u Figma-i za InfluConnect platformu.

## 🎯 Ciljevi Prototipa

1. **Demonstracija glavnih user flow-ova**
2. **Testiranje korisničkog iskustva**
3. **Validacija dizajn sistema**
4. **Prezentacija stakeholderima**

## 📱 Stranice za Prototip

### 1. Landing Stranica
**Fajl:** `01_Landing.fig`
- Hero sekcija sa CTA dugmadima
- "Ka<PERSON> funkcion<PERSON>" sekcija
- Benefiti za influencere i biznise
- Footer

**Interakcije:**
- "Registracija" → Tip korisnika stranica
- "Prijava" → Login stranica
- "Počni kao influencer" → Registracija (influencer)
- "Kreiraj kampanju" → Registracija (biznis)

### 2. Registracija Flow
**Fajlovi:** `02_Registration_Type.fig`, `03_Registration_Form.fig`

#### 2.1 Tip Korisnika
- Izbor između Influencer/Biznis
- Vizuelne kartice sa opisima

**Interakcije:**
- Influencer kartica → Influencer registracija
- Biznis kartica → Biznis registracija
- "Nazad" → Landing stranica

#### 2.2 Registracija Forma
- Email, password, ime, username
- Checkbox za uslove korišćenja
- Google registracija opcija

**Interakcije:**
- "Registruj se" → Kreiranje profila
- "Google registracija" → Google OAuth (placeholder)
- "Prijavite se" → Login stranica

### 3. Kreiranje Profila
**Fajlovi:** `04_Influencer_Profile.fig`, `05_Business_Profile.fig`

#### 3.1 Influencer Profil
- Avatar upload
- Bio, lokacija, website
- Društvene mreže (Instagram, TikTok, YouTube)
- Broj pratilaca
- Niša i cene
- Portfolio upload

#### 3.2 Biznis Profil
- Logo upload
- Naziv firme, industrija, veličina
- Opis brenda
- Budžet range
- Lokacija

**Interakcije:**
- "Sačuvaj profil" → Dashboard
- "Nazad" → Registracija forma

### 4. Dashboard
**Fajlovi:** `06_Influencer_Dashboard.fig`, `07_Business_Dashboard.fig`

#### 4.1 Influencer Dashboard
- Brzi pregled (zarada, kampanje, ocjena)
- Aktivne kampanje
- Nove ponude
- Navigacija

#### 4.2 Biznis Dashboard
- Brzi pregled (kampanje, aplikacije, potrošeno)
- Aktivne kampanje
- CTA za novu kampanju

**Interakcije:**
- "Sve kampanje" → Lista kampanja
- "Nova kampanja" → Kreiranje kampanje
- "Profil" → Pregled profila
- Menu ikona → Navigacija

### 5. Kampanje
**Fajlovi:** `08_Campaign_List.fig`, `09_Campaign_Details.fig`, `10_Create_Campaign.fig`

#### 5.1 Lista Kampanja (Influencer view)
- Pretraga i filteri
- Kartice kampanja sa osnovnim info
- Paginacija

#### 5.2 Detalji Kampanje
- Kompletne informacije o kampanji
- Biznis informacije
- Zahtjevi i deliverables
- CTA za aplikaciju

#### 5.3 Kreiranje Kampanje (Biznis)
- Multi-step forma
- Osnovne informacije
- Ciljna publika
- Budžet i rokovi
- Pregled i objava

**Interakcije:**
- Kampanja kartica → Detalji kampanje
- "Prijavi se" → Aplikacija forma
- "Nastavi" → Sledeći korak
- "Objavi kampanju" → Dashboard

### 6. Chat Sistem
**Fajlovi:** `11_Chat_List.fig`, `12_Chat_Interface.fig`

#### 6.1 Lista Razgovora
- Lista aktivnih chatova
- Pretraga
- Unread indicators

#### 6.2 Chat Interface
- Poruke sa timestamp
- Input za novu poruku
- File attachment
- Typing indicators

**Interakcije:**
- Chat iz liste → Chat interface
- "Pošalji" → Nova poruka
- Attachment ikona → File picker

### 7. Profil Stranice
**Fajlovi:** `13_Profile_View.fig`, `14_Profile_Edit.fig`

#### 7.1 Pregled Profila
- Sve informacije o korisniku
- Portfolio/radovi
- Ocjene i reviews
- Statistike

#### 7.2 Editovanje Profila
- Forme za ažuriranje informacija
- Upload novih slika
- Promjena cijena (influencer)

**Interakcije:**
- "Uredi profil" → Edit forma
- "Sačuvaj" → Profil pregled

## 🎨 Dizajn Sistem u Figma

### Boje
```
Primary Blue: #3B82F6
Success Green: #22C55E
Warning Orange: #F59E0B
Error Red: #EF4444
Neutral Grays: #F8FAFC → #0F172A
```

### Tipografija
```
Font: Inter
H1: 36px/Bold (Mobile), 48px/Bold (Desktop)
H2: 30px/SemiBold
H3: 24px/SemiBold
Body: 16px/Regular
Small: 14px/Regular
```

### Komponente
- Buttons (Primary, Secondary, Ghost)
- Cards (Default, Hover states)
- Form inputs (Default, Focus, Error states)
- Navigation (Mobile menu, Desktop nav)
- Modals i overlays

### Ikone
- Lucide React icon set
- 16px, 20px, 24px veličine
- Outline style

## 🔄 Interaktivni Flow-ovi

### Glavni Flow - Influencer
```
Landing → Registracija (Influencer) → Profil Setup → 
Dashboard → Kampanje → Detalji → Aplikacija → Chat → 
Kreiranje Sadržaja → Završetak
```

### Glavni Flow - Biznis
```
Landing → Registracija (Biznis) → Profil Setup → 
Dashboard → Nova Kampanja → Objava → Pregled Aplikacija → 
Izbor Influencera → Chat → Praćenje → Završetak
```

### Sekundarni Flow-ovi
- Login/Logout
- Profil editovanje
- Pretraga kampanja
- Notifikacije
- Settings

## 📱 Responsive Breakpoints

### Mobile (375px)
- Stack layout
- Bottom navigation
- Hamburger menu
- Touch-friendly buttons (44px min)

### Tablet (768px)
- Hybrid layout
- Side navigation možda
- Larger cards

### Desktop (1024px+)
- Full sidebar navigation
- Multi-column layouts
- Hover states

## 🎭 Micro-interakcije

### Hover States
- Button hover effects
- Card lift on hover
- Link underlines

### Loading States
- Skeleton screens
- Progress indicators
- Spinners

### Success States
- Checkmarks
- Success messages
- Confetti (za završene kampanje)

### Error States
- Error messages
- Form validation
- Retry buttons

## 📊 Prototip Struktura

```
InfluConnect_Prototype.fig
├── 📄 Cover (Naslovnica sa opisom)
├── 🎨 Design System
│   ├── Colors
│   ├── Typography
│   ├── Components
│   └── Icons
├── 📱 Mobile Screens
│   ├── Landing & Auth
│   ├── Onboarding
│   ├── Dashboard
│   ├── Campaigns
│   ├── Chat
│   └── Profile
├── 💻 Desktop Screens
│   ├── Landing & Auth
│   ├── Dashboard
│   ├── Campaigns
│   └── Profile
└── 🔄 Prototype Flows
    ├── Influencer Journey
    ├── Business Journey
    └── Shared Flows
```

## 🧪 Testiranje

### Usability Testing Scenariji
1. **Influencer registracija i prva kampanja**
2. **Biznis kreiranje kampanje i izbor influencera**
3. **Chat komunikacija između strana**
4. **Završetak kampanje i plaćanje**

### A/B Test Varijante
- CTA button tekstovi
- Registracija flow (single vs multi-step)
- Dashboard layout opcije

## 📝 Napomene za Implementaciju

1. **Koristiti Auto Layout** za responsive komponente
2. **Kreirati Component Library** za konzistentnost
3. **Dodati Variants** za različita stanja
4. **Koristiti Smart Animate** za smooth transitions
5. **Dodati Overlays** za modals i tooltips

## 🚀 Sledeći Koraci

1. Kreirati Figma fajl sa osnovnom strukturom
2. Implementirati design system komponente
3. Dizajnirati ključne stranice
4. Dodati interakcije i transitions
5. Testirati sa korisnicima
6. Iterirati na osnovu feedback-a
