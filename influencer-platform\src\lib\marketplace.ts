import { supabase } from './supabase';

// Tipovi za marketplace
export interface SearchFilters {
  search?: string;
  categories?: number[];
  platforms?: number[];
  contentTypes?: number[];
  minPrice?: number;
  maxPrice?: number;
  minFollowers?: number;
  maxFollowers?: number;
  location?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  minAge?: number;
  maxAge?: number;
  verifiedOnly?: boolean;
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'followers_desc' | 'newest';
  limit?: number;
  offset?: number;
}

export interface InfluencerSearchResult {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string;
  bio: string;
  location: string;
  gender: string;
  age: number;
  is_verified: boolean;
  categories: string[];
  platforms: Array<{
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    handle: string;
    followers_count: number;
    is_verified: boolean;
  }>;
  pricing: Array<{
    platform_id: number;
    platform_name: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
  }>;
  min_price: number;
  max_price: number;
  total_followers: number;
  relevance_score: number;
}

export interface PublicInfluencerProfile {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string;
  bio: string;
  location: string;
  gender: string;
  age: number;
  is_verified: boolean;
  categories: Array<{
    id: number;
    name: string;
    icon: string;
    is_primary: boolean;
  }>;
  platforms: Array<{
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    handle: string;
    followers_count: number;
    is_verified: boolean;
  }>;
  pricing: Array<{
    platform_id: number;
    platform_name: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
  }>;
  portfolio_urls: string[];
  total_followers: number;
  created_at: string;
}

/**
 * Pretraga influencera sa filterima
 */
export async function searchInfluencers(filters: SearchFilters = {}) {
  try {
    // Build the query using RPC function for better performance
    let query = supabase
      .rpc('get_influencers_with_details', {
        search_term: filters.search || '',
        min_followers: filters.minFollowers || 0,
        max_followers: filters.maxFollowers || 999999999,
        min_price: filters.minPrice || 0,
        max_price: filters.maxPrice || 999999,
        platform_filter: filters.platform || '',
        category_filter: filters.category || '',
        location_filter: filters.location || '',
        limit_count: 12
      });

    const { data: profiles, error } = await query;

    if (error) {
      console.error('Supabase query error:', error);
      throw error;
    }

    console.log('Raw profiles data:', profiles);
    console.log('Profiles count:', profiles?.length || 0);

    // Transform data to match InfluencerSearchResult interface
    const influencers: InfluencerSearchResult[] = (profiles || [])
      .map(profile => {
        const totalFollowers = (profile.instagram_followers || 0) +
                             (profile.tiktok_followers || 0) +
                             (profile.youtube_subscribers || 0);

      // Create platforms array based on available data
      const platforms = [];
      if (profile.instagram_followers > 0) {
        platforms.push({
          platform_id: 1,
          platform_name: 'Instagram',
          platform_icon: '📷',
          handle: `@${profile.username}`,
          followers_count: profile.instagram_followers,
          is_verified: profile.is_verified
        });
      }
      if (profile.tiktok_followers > 0) {
        platforms.push({
          platform_id: 2,
          platform_name: 'TikTok',
          platform_icon: '🎵',
          handle: `@${profile.username}`,
          followers_count: profile.tiktok_followers,
          is_verified: false
        });
      }
      if (profile.youtube_subscribers > 0) {
        platforms.push({
          platform_id: 3,
          platform_name: 'YouTube',
          platform_icon: '📺',
          handle: `@${profile.username}`,
          followers_count: profile.youtube_subscribers,
          is_verified: false
        });
      }

      // Create pricing array
      const pricing = [];
      if (profile.price_per_post) {
        pricing.push({
          platform_id: 1,
          platform_name: 'Instagram',
          content_type_id: 1,
          content_type_name: 'Post',
          price: Number(profile.price_per_post),
          currency: 'KM'
        });
      }
      if (profile.price_per_story) {
        pricing.push({
          platform_id: 1,
          platform_name: 'Instagram',
          content_type_id: 2,
          content_type_name: 'Story',
          price: Number(profile.price_per_story),
          currency: 'KM'
        });
      }
      if (profile.price_per_reel) {
        pricing.push({
          platform_id: 1,
          platform_name: 'Instagram',
          content_type_id: 3,
          content_type_name: 'Reel',
          price: Number(profile.price_per_reel),
          currency: 'KM'
        });
      }

      const minPrice = pricing.length > 0 ? Math.min(...pricing.map(p => p.price)) : 0;
      const maxPrice = pricing.length > 0 ? Math.max(...pricing.map(p => p.price)) : 0;

      return {
        id: profile.id,
        username: profile.username || '',
        full_name: profile.full_name || profile.username || '',
        avatar_url: profile.avatar_url || '',
        bio: profile.bio || '',
        location: profile.location || '',
        gender: profile.gender || 'prefer_not_to_say',
        age: profile.age || 0,
        is_verified: profile.is_verified || false,
        categories: [], // TODO: Add categories when implemented
        platforms,
        pricing,
        min_price: minPrice,
        max_price: maxPrice,
        total_followers: totalFollowers,
        relevance_score: 1.0
      };
    });

    return { data: influencers, error: null };
  } catch (error) {
    console.error('Error in searchInfluencers:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje javnog profila influencera po username-u
 */
export async function getPublicInfluencerProfile(username: string) {
  try {
    // TODO: Zameniti sa pravim RPC pozivom kada se kreira get_public_influencer_profile funkcija
    // const { data, error } = await supabase.rpc('get_public_influencer_profile', { influencer_username: username });

    // Mock profili za testiranje
    const mockProfiles: { [key: string]: PublicInfluencerProfile } = {
      'ana_fitness': {
        id: '1',
        username: 'ana_fitness',
        full_name: 'Ana Marković',
        avatar_url: '',
        bio: 'Fitness trener i nutricionista sa 5+ godina iskustva. Pomagam ljudima da postignu svoje ciljeve kroz zdrav način života, pravilnu ishranu i redovne vežbe. Specijalizovana sam za weight loss transformacije i muscle building programe.',
        location: 'Sarajevo, BiH',
        gender: 'female',
        age: 28,
        is_verified: true,
        categories: ['Fitness', 'Zdravlje', 'Lifestyle'],
        platforms: [
          {
            platform_id: 1,
            platform_name: 'Instagram',
            platform_icon: '📷',
            handle: '@ana_fitness',
            followers_count: 15000,
            is_verified: true
          },
          {
            platform_id: 3,
            platform_name: 'TikTok',
            platform_icon: '🎵',
            handle: '@ana_fitness_tiktok',
            followers_count: 8500,
            is_verified: false
          }
        ],
        pricing: [
          {
            platform_id: 1,
            platform_name: 'Instagram',
            platform_icon: '📷',
            content_type_id: 1,
            content_type_name: 'Post',
            price: 150,
            currency: 'KM'
          },
          {
            platform_id: 1,
            platform_name: 'Instagram',
            platform_icon: '📷',
            content_type_id: 2,
            content_type_name: 'Story',
            price: 80,
            currency: 'KM'
          },
          {
            platform_id: 1,
            platform_name: 'Instagram',
            platform_icon: '📷',
            content_type_id: 3,
            content_type_name: 'Reel',
            price: 200,
            currency: 'KM'
          },
          {
            platform_id: 3,
            platform_name: 'TikTok',
            platform_icon: '🎵',
            content_type_id: 6,
            content_type_name: 'Video',
            price: 120,
            currency: 'KM'
          }
        ],
        min_price: 80,
        max_price: 200,
        total_followers: 23500,
        portfolio_items: [
          {
            id: '1',
            platform_name: 'Instagram',
            content_type: 'Post',
            media_url: '',
            description: 'Workout routine za početnice',
            engagement_rate: 4.2,
            created_at: '2024-01-15'
          }
        ]
      },
      'marko_tech': {
        id: '2',
        username: 'marko_tech',
        full_name: 'Marko Petrović',
        avatar_url: '',
        bio: 'Tech reviewer i programer. Testiram najnovije gadgete, aplikacije i tehnologije. Radim kao senior developer i dijelim svoje znanje kroz video sadržaj. Specijalizovan za mobile development i AI tehnologije.',
        location: 'Banja Luka, BiH',
        gender: 'male',
        age: 32,
        is_verified: false,
        categories: ['Tehnologija', 'Gaming', 'Edukacija'],
        platforms: [
          {
            platform_id: 2,
            platform_name: 'YouTube',
            platform_icon: '📺',
            handle: '@marko_tech',
            followers_count: 8500,
            is_verified: false
          },
          {
            platform_id: 1,
            platform_name: 'Instagram',
            platform_icon: '📷',
            handle: '@marko.tech.reviews',
            followers_count: 5200,
            is_verified: false
          }
        ],
        pricing: [
          {
            platform_id: 2,
            platform_name: 'YouTube',
            platform_icon: '📺',
            content_type_id: 4,
            content_type_name: 'Video',
            price: 300,
            currency: 'KM'
          },
          {
            platform_id: 2,
            platform_name: 'YouTube',
            platform_icon: '📺',
            content_type_id: 5,
            content_type_name: 'Short',
            price: 150,
            currency: 'KM'
          },
          {
            platform_id: 1,
            platform_name: 'Instagram',
            platform_icon: '📷',
            content_type_id: 1,
            content_type_name: 'Post',
            price: 100,
            currency: 'KM'
          }
        ],
        min_price: 100,
        max_price: 300,
        total_followers: 13700,
        portfolio_items: [
          {
            id: '2',
            platform_name: 'YouTube',
            content_type: 'Video',
            media_url: '',
            description: 'iPhone 15 Pro Max review',
            engagement_rate: 6.8,
            created_at: '2024-01-10'
          }
        ]
      }
    };

    const profile = mockProfiles[username];

    if (!profile) {
      return { data: null, error: { message: 'Profile not found' } };
    }

    return { data: profile, error: null };
  } catch (error) {
    console.error('Error in getPublicInfluencerProfile:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje svih kategorija za filter
 */
export async function getCategories() {
  try {
    // TODO: Zameniti sa pravim pozivom kada se kreira categories tabela
    // const { data, error } = await supabase.from('categories').select('id, name, slug, icon').order('name');

    // Mock kategorije
    const mockCategories = [
      { id: 1, name: 'Fitness', slug: 'fitness', icon: '💪' },
      { id: 2, name: 'Moda', slug: 'moda', icon: '👗' },
      { id: 3, name: 'Tehnologija', slug: 'tehnologija', icon: '📱' },
      { id: 4, name: 'Hrana', slug: 'hrana', icon: '🍕' },
      { id: 5, name: 'Putovanja', slug: 'putovanja', icon: '✈️' },
      { id: 6, name: 'Gaming', slug: 'gaming', icon: '🎮' },
      { id: 7, name: 'Ljepota', slug: 'ljepota', icon: '💄' },
      { id: 8, name: 'Zdravlje', slug: 'zdravlje', icon: '🏥' }
    ];

    return { data: mockCategories, error: null };
  } catch (error) {
    console.error('Error in getCategories:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje svih platformi za filter
 */
export async function getPlatforms() {
  try {
    // TODO: Zameniti sa pravim pozivom kada se kreira platforms tabela
    // const { data, error } = await supabase.from('platforms').select('id, name, slug, icon').eq('is_active', true).order('name');

    // Mock platforme
    const mockPlatforms = [
      { id: 1, name: 'Instagram', slug: 'instagram', icon: '📷' },
      { id: 2, name: 'YouTube', slug: 'youtube', icon: '📺' },
      { id: 3, name: 'TikTok', slug: 'tiktok', icon: '🎵' },
      { id: 4, name: 'Facebook', slug: 'facebook', icon: '📘' },
      { id: 5, name: 'Twitter', slug: 'twitter', icon: '🐦' },
      { id: 6, name: 'LinkedIn', slug: 'linkedin', icon: '💼' }
    ];

    return { data: mockPlatforms, error: null };
  } catch (error) {
    console.error('Error in getPlatforms:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje content tipova za određene platforme
 */
export async function getContentTypes(platformIds?: number[]) {
  try {
    // TODO: Zameniti sa pravim pozivom kada se kreira content_types tabela

    // Mock content tipovi
    const mockContentTypes = [
      { id: 1, platform_id: 1, name: 'Post', slug: 'post', description: 'Obična objava', platforms: { name: 'Instagram', icon: '📷' } },
      { id: 2, platform_id: 1, name: 'Story', slug: 'story', description: 'Instagram story', platforms: { name: 'Instagram', icon: '📷' } },
      { id: 3, platform_id: 1, name: 'Reel', slug: 'reel', description: 'Instagram reel', platforms: { name: 'Instagram', icon: '📷' } },
      { id: 4, platform_id: 2, name: 'Video', slug: 'video', description: 'YouTube video', platforms: { name: 'YouTube', icon: '📺' } },
      { id: 5, platform_id: 2, name: 'Short', slug: 'short', description: 'YouTube short', platforms: { name: 'YouTube', icon: '📺' } },
      { id: 6, platform_id: 3, name: 'Video', slug: 'tiktok-video', description: 'TikTok video', platforms: { name: 'TikTok', icon: '🎵' } }
    ];

    let filteredContentTypes = mockContentTypes;
    if (platformIds && platformIds.length > 0) {
      filteredContentTypes = mockContentTypes.filter(ct => platformIds.includes(ct.platform_id));
    }

    return { data: filteredContentTypes, error: null };
  } catch (error) {
    console.error('Error in getContentTypes:', error);
    return { data: null, error };
  }
}

/**
 * Refresh materialized view (admin funkcija)
 */
export async function refreshSearchView() {
  try {
    const { data, error } = await supabase.rpc('refresh_influencer_search_view');

    if (error) {
      console.error('Error refreshing search view:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error in refreshSearchView:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje statistika za marketplace (broj influencera, kategorija, itd.)
 */
export async function getMarketplaceStats() {
  try {
    // Broj influencera
    const { count: influencersCount } = await supabase
      .from('influencer_search_view')
      .select('*', { count: 'exact', head: true });

    // Broj kategorija
    const { count: categoriesCount } = await supabase
      .from('categories')
      .select('*', { count: 'exact', head: true });

    // Broj platformi
    const { count: platformsCount } = await supabase
      .from('platforms')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Prosečna cijena
    const { data: avgPriceData } = await supabase
      .from('influencer_platform_pricing')
      .select('price')
      .eq('is_available', true);

    const avgPrice = avgPriceData && avgPriceData.length > 0
      ? avgPriceData.reduce((sum, item) => sum + (item.price || 0), 0) / avgPriceData.length
      : 0;

    return {
      data: {
        influencersCount: influencersCount || 0,
        categoriesCount: categoriesCount || 0,
        platformsCount: platformsCount || 0,
        averagePrice: Math.round(avgPrice)
      },
      error: null
    };
  } catch (error) {
    console.error('Error in getMarketplaceStats:', error);
    return { data: null, error };
  }
}
