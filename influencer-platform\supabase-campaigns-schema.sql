-- FAZA 3B: Database schema za kampanje i aplikacije
-- Pokrenuti u Supabase SQL Editor

-- 1. <PERSON>reiran<PERSON> enum tipova za kampanje
CREATE TYPE campaign_status AS ENUM ('draft', 'active', 'paused', 'completed', 'cancelled');
CREATE TYPE application_status AS ENUM ('pending', 'accepted', 'rejected', 'completed');
CREATE TYPE campaign_priority AS ENUM ('low', 'medium', 'high', 'urgent');

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> campaigns tabele (dodavanje novih polja)
ALTER TABLE campaigns 
ADD COLUMN IF NOT EXISTS priority campaign_priority DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS location VARCHAR(100),
ADD COLUMN IF NOT EXISTS min_followers INTEGER,
ADD COLUMN IF NOT EXISTS max_followers INTEGER,
ADD COLUMN IF NOT EXISTS age_range_min INTEGER,
ADD COLUMN IF NOT EXISTS age_range_max INTEGER,
ADD COLUMN IF NOT EXISTS gender VARCHAR(20),
ADD COLUMN IF NOT EXISTS application_deadline DATE,
ADD COLUMN IF NOT EXISTS max_applications INTEGER DEFAULT 50,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS brief_file_url TEXT,
ADD COLUMN IF NOT EXISTS reference_urls TEXT[],
ADD COLUMN IF NOT EXISTS hashtags TEXT[],
ADD COLUMN IF NOT EXISTS do_not_mention TEXT[],
ADD COLUMN IF NOT EXISTS collaboration_type VARCHAR(50) DEFAULT 'paid', -- 'paid', 'barter', 'hybrid'
ADD COLUMN IF NOT EXISTS payment_terms VARCHAR(100), -- 'upfront', '50_50', 'on_delivery'
ADD COLUMN IF NOT EXISTS usage_rights VARCHAR(100), -- 'single_use', 'unlimited', '6_months', '1_year'
ADD COLUMN IF NOT EXISTS exclusivity_period INTEGER, -- days
ADD COLUMN IF NOT EXISTS revisions_included INTEGER DEFAULT 2,
ADD COLUMN IF NOT EXISTS views_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS applications_count INTEGER DEFAULT 0;

-- 3. Kreiranje campaign_platforms tabele (many-to-many)
CREATE TABLE IF NOT EXISTS campaign_platforms (
  id SERIAL PRIMARY KEY,
  campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  content_type_ids INTEGER[] NOT NULL,
  posts_required INTEGER DEFAULT 1,
  budget_per_post DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, platform_id)
);

-- 4. Kreiranje campaign_categories tabele (many-to-many)
CREATE TABLE IF NOT EXISTS campaign_categories (
  id SERIAL PRIMARY KEY,
  campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, category_id)
);

-- 5. Kreiranje campaign_applications tabele
CREATE TABLE IF NOT EXISTS campaign_applications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  status application_status DEFAULT 'pending',
  proposed_price DECIMAL(10,2),
  message TEXT NOT NULL,
  portfolio_urls TEXT[],
  estimated_delivery_days INTEGER,
  additional_services TEXT,
  terms_accepted BOOLEAN DEFAULT FALSE,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  business_feedback TEXT,
  rejection_reason TEXT,
  influencer_rating INTEGER CHECK (influencer_rating >= 1 AND influencer_rating <= 5),
  business_rating INTEGER CHECK (business_rating >= 1 AND business_rating <= 5),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, influencer_id)
);

-- 6. Kreiranje campaign_deliverables tabele
CREATE TABLE IF NOT EXISTS campaign_deliverables (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  application_id UUID REFERENCES campaign_applications(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  file_url TEXT,
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITH TIME ZONE,
  revision_requested BOOLEAN DEFAULT FALSE,
  revision_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Kreiranje indexes za performanse
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns(status);
CREATE INDEX IF NOT EXISTS idx_campaigns_business_id ON campaigns(business_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_created_at ON campaigns(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_campaigns_application_deadline ON campaigns(application_deadline);
CREATE INDEX IF NOT EXISTS idx_campaigns_budget ON campaigns(budget);
CREATE INDEX IF NOT EXISTS idx_campaigns_location ON campaigns(location);
CREATE INDEX IF NOT EXISTS idx_campaigns_featured ON campaigns(is_featured, status);

CREATE INDEX IF NOT EXISTS idx_campaign_applications_campaign_id ON campaign_applications(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_applications_influencer_id ON campaign_applications(influencer_id);
CREATE INDEX IF NOT EXISTS idx_campaign_applications_status ON campaign_applications(status);
CREATE INDEX IF NOT EXISTS idx_campaign_applications_applied_at ON campaign_applications(applied_at DESC);

CREATE INDEX IF NOT EXISTS idx_campaign_platforms_campaign_id ON campaign_platforms(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_categories_campaign_id ON campaign_categories(campaign_id);

-- 8. Kreiranje RLS policies za campaigns
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;

-- Businesses mogu videti samo svoje kampanje
CREATE POLICY "Businesses can view own campaigns" ON campaigns
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM businesses WHERE id = auth.uid()
    )
  );

-- Businesses mogu kreirati kampanje
CREATE POLICY "Businesses can create campaigns" ON campaigns
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM businesses WHERE id = auth.uid()
    )
  );

-- Businesses mogu ažurirati svoje kampanje
CREATE POLICY "Businesses can update own campaigns" ON campaigns
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM businesses WHERE id = auth.uid()
    )
  );

-- Influenceri mogu videti aktivne kampanje
CREATE POLICY "Influencers can view active campaigns" ON campaigns
  FOR SELECT USING (
    status = 'active' AND 
    (application_deadline IS NULL OR application_deadline >= CURRENT_DATE)
  );

-- 9. RLS policies za campaign_applications
ALTER TABLE campaign_applications ENABLE ROW LEVEL SECURITY;

-- Influenceri mogu videti svoje aplikacije
CREATE POLICY "Influencers can view own applications" ON campaign_applications
  FOR SELECT USING (
    influencer_id IN (
      SELECT id FROM influencers WHERE id = auth.uid()
    )
  );

-- Businesses mogu videti aplikacije na svoje kampanje
CREATE POLICY "Businesses can view applications to own campaigns" ON campaign_applications
  FOR SELECT USING (
    campaign_id IN (
      SELECT id FROM campaigns WHERE business_id = auth.uid()
    )
  );

-- Influenceri mogu kreirati aplikacije
CREATE POLICY "Influencers can create applications" ON campaign_applications
  FOR INSERT WITH CHECK (
    influencer_id IN (
      SELECT id FROM influencers WHERE id = auth.uid()
    )
  );

-- Businesses mogu ažurirati status aplikacija
CREATE POLICY "Businesses can update application status" ON campaign_applications
  FOR UPDATE USING (
    campaign_id IN (
      SELECT id FROM campaigns WHERE business_id = auth.uid()
    )
  );

-- 10. RLS policies za ostale tabele
ALTER TABLE campaign_platforms ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_deliverables ENABLE ROW LEVEL SECURITY;

-- Campaign platforms - povezano sa campaigns permissions
CREATE POLICY "Campaign platforms follow campaign permissions" ON campaign_platforms
  FOR ALL USING (
    campaign_id IN (
      SELECT id FROM campaigns WHERE 
        business_id = auth.uid() OR 
        (status = 'active' AND (application_deadline IS NULL OR application_deadline >= CURRENT_DATE))
    )
  );

-- Campaign categories - povezano sa campaigns permissions  
CREATE POLICY "Campaign categories follow campaign permissions" ON campaign_categories
  FOR ALL USING (
    campaign_id IN (
      SELECT id FROM campaigns WHERE 
        business_id = auth.uid() OR 
        (status = 'active' AND (application_deadline IS NULL OR application_deadline >= CURRENT_DATE))
    )
  );

-- Campaign deliverables - samo vlasnici aplikacije i business
CREATE POLICY "Campaign deliverables access control" ON campaign_deliverables
  FOR ALL USING (
    application_id IN (
      SELECT id FROM campaign_applications WHERE 
        influencer_id = auth.uid() OR 
        campaign_id IN (SELECT id FROM campaigns WHERE business_id = auth.uid())
    )
  );

-- 11. Kreiranje funkcije za ažuriranje applications_count
CREATE OR REPLACE FUNCTION update_campaign_applications_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE campaigns 
    SET applications_count = applications_count + 1 
    WHERE id = NEW.campaign_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE campaigns 
    SET applications_count = applications_count - 1 
    WHERE id = OLD.campaign_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 12. Kreiranje trigger-a
CREATE TRIGGER trigger_update_applications_count
  AFTER INSERT OR DELETE ON campaign_applications
  FOR EACH ROW EXECUTE FUNCTION update_campaign_applications_count();

-- 13. Kreiranje funkcije za ažuriranje views_count
CREATE OR REPLACE FUNCTION increment_campaign_views(campaign_uuid UUID)
RETURNS void AS $$
BEGIN
  UPDATE campaigns 
  SET views_count = views_count + 1 
  WHERE id = campaign_uuid;
END;
$$ LANGUAGE plpgsql;

-- 14. Kreiranje view za kampanje sa dodatnim informacijama (simplified version)
CREATE OR REPLACE VIEW campaigns_with_details AS
SELECT
  c.*,
  b.company_name,
  b.industry,
  p.username as business_username,
  p.avatar_url as business_avatar,
  '[]'::json as platforms,
  '[]'::json as categories
FROM campaigns c
LEFT JOIN businesses b ON c.business_id = b.id
LEFT JOIN profiles p ON b.id = p.id;

-- 14. RPC funkcija za kreiranje kampanje (bypass RLS issues)
-- Drop old function first
DROP FUNCTION IF EXISTS create_business_campaign(UUID, TEXT, TEXT, INTEGER, campaign_status);
DROP FUNCTION IF EXISTS create_business_campaign(UUID, VARCHAR(200), TEXT, DECIMAL(10,2), campaign_status);

CREATE OR REPLACE FUNCTION create_campaign_for_business(
  p_business_id UUID,
  p_title VARCHAR(200),
  p_description TEXT,
  p_budget DECIMAL(10,2),
  p_content_types content_type[],
  p_status campaign_status DEFAULT 'draft'
)
RETURNS TABLE(id UUID, title VARCHAR(200), description TEXT, budget DECIMAL(10,2), status campaign_status, created_at TIMESTAMPTZ)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is authenticated and is a business
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Not authenticated';
  END IF;

  IF NOT EXISTS (SELECT 1 FROM businesses WHERE businesses.id = auth.uid()) THEN
    RAISE EXCEPTION 'User is not a business';
  END IF;

  -- Insert campaign with provided content_types
  RETURN QUERY
  INSERT INTO campaigns (business_id, title, description, budget, status, content_types)
  VALUES (p_business_id, p_title, p_description, p_budget, p_status, p_content_types)
  RETURNING campaigns.id, campaigns.title, campaigns.description, campaigns.budget, campaigns.status, campaigns.created_at;
END;
$$;

-- 15. Kreiranje RPC funkcije za increment views
CREATE OR REPLACE FUNCTION increment_campaign_views(campaign_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE campaigns
  SET views_count = COALESCE(views_count, 0) + 1
  WHERE id = campaign_id;
END;
$$ LANGUAGE plpgsql;

-- 16. Kreiranje materialized view za brže pretrage
CREATE MATERIALIZED VIEW IF NOT EXISTS campaigns_search_view AS
SELECT 
  c.id,
  c.title,
  c.description,
  c.budget,
  c.status,
  c.location,
  c.application_deadline,
  c.min_followers,
  c.max_followers,
  c.age_range_min,
  c.age_range_max,
  c.gender,
  c.is_featured,
  c.applications_count,
  c.views_count,
  c.created_at,
  b.company_name,
  b.industry,
  p.username as business_username,
  p.avatar_url as business_avatar,
  -- Full-text search vector
  to_tsvector('english', 
    COALESCE(c.title, '') || ' ' || 
    COALESCE(c.description, '') || ' ' || 
    COALESCE(b.company_name, '') || ' ' ||
    COALESCE(b.industry, '')
  ) as search_vector,
  -- Platforms kao JSON
  COALESCE(
    json_agg(
      DISTINCT jsonb_build_object(
        'platform_id', cp.platform_id,
        'platform_name', pl.name,
        'platform_icon', pl.icon,
        'content_type_ids', cp.content_type_ids,
        'posts_required', cp.posts_required,
        'budget_per_post', cp.budget_per_post
      )
    ) FILTER (WHERE cp.platform_id IS NOT NULL), 
    '[]'::json
  ) as platforms,
  -- Categories kao JSON
  COALESCE(
    json_agg(
      DISTINCT jsonb_build_object(
        'category_id', cc.category_id,
        'category_name', cat.name,
        'category_icon', cat.icon
      )
    ) FILTER (WHERE cc.category_id IS NOT NULL), 
    '[]'::json
  ) as categories,
  -- Platform IDs za filtriranje
  COALESCE(array_agg(DISTINCT cp.platform_id) FILTER (WHERE cp.platform_id IS NOT NULL), ARRAY[]::integer[]) as platform_ids,
  -- Category IDs za filtriranje
  COALESCE(array_agg(DISTINCT cc.category_id) FILTER (WHERE cc.category_id IS NOT NULL), ARRAY[]::integer[]) as category_ids
FROM campaigns c
LEFT JOIN businesses b ON c.business_id = b.id
LEFT JOIN profiles p ON b.id = p.id
LEFT JOIN campaign_platforms cp ON c.id = cp.campaign_id
LEFT JOIN platforms pl ON cp.platform_id = pl.id
LEFT JOIN campaign_categories cc ON c.id = cc.campaign_id
LEFT JOIN categories cat ON cc.category_id = cat.id
WHERE c.status = 'active'
GROUP BY c.id, b.company_name, b.industry, p.username, p.avatar_url;

-- 16. Kreiranje GIN index za full-text search
CREATE INDEX IF NOT EXISTS idx_campaigns_search_vector ON campaigns_search_view USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_campaigns_platform_ids ON campaigns_search_view USING GIN(platform_ids);
CREATE INDEX IF NOT EXISTS idx_campaigns_category_ids ON campaigns_search_view USING GIN(category_ids);

-- 17. Funkcija za refresh materialized view
CREATE OR REPLACE FUNCTION refresh_campaigns_search_view()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY campaigns_search_view;
EXCEPTION
  WHEN OTHERS THEN
    REFRESH MATERIALIZED VIEW campaigns_search_view;
END;
$$ LANGUAGE plpgsql;

-- 18. Kreiranje trigger za auto-refresh materialized view
CREATE OR REPLACE FUNCTION trigger_refresh_campaigns_search()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM refresh_campaigns_search_view();
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger se pokreće nakon INSERT/UPDATE/DELETE na campaigns
CREATE TRIGGER trigger_campaigns_search_refresh
  AFTER INSERT OR UPDATE OR DELETE ON campaigns
  FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_campaigns_search();

-- Trigger se pokreće nakon promene platformi/kategorija
CREATE TRIGGER trigger_campaigns_platforms_refresh
  AFTER INSERT OR UPDATE OR DELETE ON campaign_platforms
  FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_campaigns_search();

CREATE TRIGGER trigger_campaigns_categories_refresh
  AFTER INSERT OR UPDATE OR DELETE ON campaign_categories
  FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_campaigns_search();

-- 19. RPC funkcija za dohvatanje aktivnih kampanja za influencere (zaobilazi RLS)
CREATE OR REPLACE FUNCTION get_active_campaigns_for_influencers()
RETURNS TABLE(
  id UUID,
  title VARCHAR(200),
  description TEXT,
  budget DECIMAL(10,2),
  status campaign_status,
  created_at TIMESTAMPTZ,
  business_id UUID,
  content_types content_type[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Provjeri da li je korisnik influencer
  IF NOT EXISTS (SELECT 1 FROM influencers WHERE influencers.id = auth.uid()) THEN
    RAISE EXCEPTION 'User is not an influencer';
  END IF;

  -- Vrati aktivne kampanje sa osnovnim poljima
  RETURN QUERY
  SELECT
    c.id,
    c.title,
    c.description,
    c.budget,
    c.status,
    c.created_at,
    c.business_id,
    c.content_types
  FROM campaigns c
  WHERE c.status = 'active';
END;
$$;
