# Design System - Influencer Marketing Platforma

## 🎨 Mobile-First Pristup

Dizajn sistem je kreiran sa mobile-first pristupom, optimizovan za bosanski jezik i lokalnu kulturu.

## 🎯 Brand Identitet

### Vrijednosti
- **Transparentnost** - Jasne informacije o cijenama i uslovima
- **Sigurnost** - Zaštićene transakcije i privatnost
- **Jednostavnost** - Intuitivno korisnič<PERSON> iskustvo
- **Lokalnost** - Prilagođeno bosanskom tržištu

### Ton komunikacije
- Prijateljski i profesionalan
- Direktan i jasan
- Podržavajući i ohrabrujući

## 🎨 Paleta Boja

### Primarne boje
```css
/* Glavna brand boja - Plava */
--primary-50: #eff6ff;
--primary-100: #dbeafe;
--primary-200: #bfdbfe;
--primary-300: #93c5fd;
--primary-400: #60a5fa;
--primary-500: #3b82f6; /* Glavna */
--primary-600: #2563eb;
--primary-700: #1d4ed8;
--primary-800: #1e40af;
--primary-900: #1e3a8a;

/* Sekundarna boja - Zelena (za uspjeh) */
--success-50: #f0fdf4;
--success-100: #dcfce7;
--success-200: #bbf7d0;
--success-300: #86efac;
--success-400: #4ade80;
--success-500: #22c55e; /* Glavna */
--success-600: #16a34a;
--success-700: #15803d;
--success-800: #166534;
--success-900: #14532d;
```

### Neutralne boje
```css
/* Stone paleta (Shadcn/ui) */
--stone-50: #fafaf9;
--stone-100: #f5f5f4;
--stone-200: #e7e5e4;
--stone-300: #d6d3d1;
--stone-400: #a8a29e;
--stone-500: #78716c;
--stone-600: #57534e;
--stone-700: #44403c;
--stone-800: #292524;
--stone-900: #1c1917;
```

### Semantičke boje
```css
/* Upozorenja i greške */
--warning-500: #f59e0b;
--error-500: #ef4444;
--info-500: #06b6d4;

/* Pozadine */
--background: #ffffff;
--background-secondary: #fafaf9;
--surface: #ffffff;
--surface-secondary: #f5f5f4;
```

## 📝 Tipografija

### Font Stack
```css
/* Primarni font - Inter */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

/* Sekundarni font - za headings */
--font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

/* Monospace - za kodove */
--font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
```

### Tipografska skala (Mobile-first)

#### Mobile (320px+)
```css
/* Headings */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */

/* Line heights */
--leading-tight: 1.25;
--leading-normal: 1.5;
--leading-relaxed: 1.625;
```

#### Tablet (768px+)
```css
--text-3xl: 2.25rem;   /* 36px */
--text-4xl: 3rem;      /* 48px */
```

#### Desktop (1024px+)
```css
--text-4xl: 3.75rem;   /* 60px */
--text-5xl: 4.5rem;    /* 72px */
```

### Font Weights
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

## 📏 Spacing System

### Spacing Scale (rem based)
```css
--space-0: 0;
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
--space-16: 4rem;      /* 64px */
--space-20: 5rem;      /* 80px */
--space-24: 6rem;      /* 96px */
```

### Container Sizes
```css
/* Mobile first containers */
--container-sm: 100%;      /* Mobile */
--container-md: 768px;     /* Tablet */
--container-lg: 1024px;    /* Desktop */
--container-xl: 1280px;    /* Large desktop */
```

## 🔲 Komponente

### Buttons

#### Veličine
```css
/* Small */
.btn-sm {
  padding: 0.5rem 1rem;     /* 8px 16px */
  font-size: 0.875rem;      /* 14px */
  min-height: 2rem;         /* 32px */
}

/* Medium (default) */
.btn-md {
  padding: 0.75rem 1.5rem;  /* 12px 24px */
  font-size: 1rem;          /* 16px */
  min-height: 2.75rem;      /* 44px - touch friendly */
}

/* Large */
.btn-lg {
  padding: 1rem 2rem;       /* 16px 32px */
  font-size: 1.125rem;      /* 18px */
  min-height: 3rem;         /* 48px */
}
```

#### Varijante
```css
/* Primary */
.btn-primary {
  background: var(--primary-500);
  color: white;
  border: 1px solid var(--primary-500);
}

/* Secondary */
.btn-secondary {
  background: transparent;
  color: var(--primary-500);
  border: 1px solid var(--primary-500);
}

/* Ghost */
.btn-ghost {
  background: transparent;
  color: var(--stone-700);
  border: 1px solid transparent;
}
```

### Cards
```css
.card {
  background: var(--surface);
  border: 1px solid var(--stone-200);
  border-radius: 0.75rem;   /* 12px */
  padding: var(--space-6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-content {
  margin-bottom: var(--space-4);
}

.card-footer {
  border-top: 1px solid var(--stone-200);
  padding-top: var(--space-4);
}
```

### Form Elements
```css
.input {
  width: 100%;
  padding: 0.75rem 1rem;    /* 12px 16px */
  font-size: 1rem;          /* 16px - prevents zoom on iOS */
  border: 1px solid var(--stone-300);
  border-radius: 0.5rem;    /* 8px */
  min-height: 2.75rem;      /* 44px - touch friendly */
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.label {
  font-size: 0.875rem;      /* 14px */
  font-weight: var(--font-medium);
  color: var(--stone-700);
  margin-bottom: var(--space-2);
}
```

## 📱 Responsive Breakpoints

```css
/* Mobile first approach */
@media (min-width: 640px) {  /* sm */
  /* Small tablets */
}

@media (min-width: 768px) {  /* md */
  /* Tablets */
}

@media (min-width: 1024px) { /* lg */
  /* Desktop */
}

@media (min-width: 1280px) { /* xl */
  /* Large desktop */
}
```

## 🎭 Animacije

### Transition Durations
```css
--duration-fast: 150ms;
--duration-normal: 300ms;
--duration-slow: 500ms;
```

### Easing Functions
```css
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
```

### Hover Effects
```css
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}
```

## 🌐 Lokalizacija

### Bosanski jezik specifičnosti
- Korišćenje ć, č, š, ž, đ karaktera
- Duži tekstovi zbog deklinacije
- RTL podrška nije potrebna
- Lokalni formati datuma (dd.mm.yyyy)
- Lokalna valuta (KM - Konvertibilna marka)

### Kulturne prilagodbe
- Formalniji ton u poslovnoj komunikaciji
- Jasno označavanje cijena u KM
- Lokalne reference i primjeri
- Podrška za lokalne banke i payment metode
