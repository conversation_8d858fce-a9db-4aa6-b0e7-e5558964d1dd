# Kategorije i Profili - Specifikacija

## 🎯 **KATEGORIJE SISTEMA**

### **Predefinisane kategorije** (influencer i biznis biraju iste):

```
1. Moda
2. Ljepota
3. Putovanja
4. Zdravlje i fitness
5. Hrana i piće
6. Komedija i zabava
7. Umjetnost i fotografija
8. Muzika i ples
9. Porodica i djeca
10. Preduzetništvo i biznis
11. Životinje i kućni ljubimci
12. Edukacija
13. Avantura i priroda
14. Sport i atletika
15. Tehnologija
16. Gaming
17. Zdravstvo
18. Automobilizam
19. Zanatstvo
```

### **Platforme**:
- Instagram
- TikTok  
- YouTube
- Facebook
- Twitter/X

### **Content Types po platformi**:

**Instagram:**
- Photo Feed Post
- Reel
- Story
- Live
- IGTV

**TikTok:**
- Video Post
- Live Stream

**YouTube:**
- Video (Short-form)
- Video (Long-form)
- YouTube Shorts
- Live Stream

---

## 👤 **INFLUENCER PROFIL - Ažuriran**

### **Osnovne informacije:**
- Username ✅
- Full name ✅
- Bio ✅
- Location ✅
- Website ✅
- **🆕 Gender** (Male, Female, Other, Prefer not to say)
- **🆕 Age** (broj godina)
- **🆕 Primary Categories** (minimum 1, maksimum 3)

### **Platforme i sadržaj:**
- **🆕 Instagram:**
  - Handle ✅
  - Followers ✅
  - **🆕 Content Types** (Photo Post, Reel, Story, Live)
  - **🆕 Engagement Rate** (%)
  - **🆕 Average Views** (za Reels)

- **🆕 TikTok:**
  - Handle ✅
  - Followers ✅
  - **🆕 Content Types** (Video Post, Live)
  - **🆕 Engagement Rate** (%)
  - **🆕 Average Views**

- **🆕 YouTube:**
  - Handle ✅
  - Subscribers ✅
  - **🆕 Content Types** (Short-form, Long-form, Shorts, Live)
  - **🆕 Average Views**

### **Cijene po content type:**
- **Instagram:**
  - Photo Feed Post ✅
  - Reel ✅
  - Story ✅
  - **🆕 Live Stream**
  
- **TikTok:**
  - **🆕 Video Post**
  - **🆕 Live Stream**
  
- **YouTube:**
  - **🆕 Short Video**
  - **🆕 Long Video**
  - **🆕 YouTube Shorts**

### **Portfolio:**
- **🆕 Portfolio URLs** (linkovi na prethodne radove)
- **🆕 Media uploads** (slike/videi direktno na platformu)

---

## 🏢 **BIZNIS PROFIL - Ažuriran**

### **Osnovne informacije:**
- Username ✅
- Company name ✅
- Bio ✅
- Location ✅
- Website ✅
- Industry ✅
- Company size ✅
- Budget range ✅

### **Target audience:**
- **🆕 Target Categories** (koje kategorije influencera traže)
- **🆕 Target Platforms** (Instagram, TikTok, YouTube)
- **🆕 Target Content Types** (Photo Post, Reel, Story, itd.)
- **🆕 Target Demographics:**
  - Age range (18-24, 25-34, 35-44, 45-54, 55+)
  - Gender preference (Any, Male, Female)
  - Location preference (Local, Regional, National, International)

### **Campaign preferences:**
- **🆕 Preferred follower range** (1K-10K, 10K-100K, 100K-1M, 1M+)
- **🆕 Budget per campaign** (500-2000, 2000-5000, 5000-10000, 10000+)
- **🆕 Campaign frequency** (Weekly, Monthly, Quarterly, As needed)

---

## 🔍 **FILTER SISTEM - Browse & Hire**

### **Biznis pretražuje influencere po:**

1. **Platform** (Instagram, TikTok, YouTube)
2. **Category** (Fashion, Beauty, Travel, itd.)
3. **Content Type** (Photo Post, Reel, Story, itd.)
4. **Followers** (1K-10K, 10K-100K, 100K-1M, 1M+)
5. **Location** (City, Country, Region)
6. **Price Range** (po content type)
7. **Gender** (Any, Male, Female, Other)
8. **Age** (18-24, 25-34, 35-44, 45-54, 55+)
9. **Engagement Rate** (1-3%, 3-6%, 6%+)

### **Sortiranje:**
- Price (Low to High, High to Low)
- Followers (Most to Least, Least to Most)
- Engagement Rate (Highest first)
- Recently Active
- Best Match (algoritam)

---

## 🎯 **FAZA 2D: PLATFORME I CONTENT TIPOVI**

### **Problem sa trenutnim sistemom:**
- Imamo samo osnovne cijene: `price_per_post`, `price_per_story`, `price_per_reel`
- Nije jasno za koju platformu su cijene
- Biznis ne može filtrirati po specifičnom content tipu
- Influencer ne može reći šta tačno nudi na kojoj platformi

### **Novi strukturiran sistem:**

#### **1. PLATFORME:**
```
- Instagram
- TikTok
- YouTube
- Facebook (opciono)
- Twitter/X (opciono)
```

#### **2. CONTENT TIPOVI PO PLATFORMI:**

**Instagram:**
- Photo Feed Post
- Reel
- Story
- Live Stream
- IGTV

**TikTok:**
- Video Post
- Live Stream

**YouTube:**
- Short Video (< 1 min)
- Long Video (> 1 min)
- YouTube Shorts
- Live Stream

#### **3. INFLUENCER WORKFLOW:**
1. **Izabere platforme** - na kojima je aktivan
2. **Izabere content tipove** - šta nudi na svakoj platformi
3. **Postavi cijene** - za svaki content tip koji nudi

#### **4. BIZNIS WORKFLOW:**
1. **Filtrira po platformi** - Instagram, TikTok, YouTube
2. **Filtrira po content tipu** - Photo Post, Reel, Video, itd.
3. **Filtrira po cijeni** - range za specifični content tip

#### **5. PRIMJER:**
**Influencer Marko:**
- **Instagram**: Photo Post (150 KM), Reel (200 KM), Story (50 KM)
- **TikTok**: Video Post (180 KM)
- **YouTube**: Short Video (100 KM)

**Biznis traži:**
- **Platforma**: Instagram
- **Content tip**: Reel
- **Cijena**: 150-250 KM
- **Rezultat**: Marko se pojavljuje (200 KM za Reel)

---

## 🗄️ **DATABASE SCHEMA - Ažuriranje**

### **NOVE TABELE ZA PLATFORME I CONTENT TIPOVE:**

#### **Nova tabela: platforms**
```sql
CREATE TABLE platforms (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  slug VARCHAR(50) NOT NULL UNIQUE,
  icon VARCHAR(10),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Predefinisane platforme
INSERT INTO platforms (name, slug, icon) VALUES
('Instagram', 'instagram', '📷'),
('TikTok', 'tiktok', '🎵'),
('YouTube', 'youtube', '📺'),
('Facebook', 'facebook', '👥'),
('Twitter/X', 'twitter', '🐦');
```

#### **Nova tabela: content_types**
```sql
CREATE TABLE content_types (
  id SERIAL PRIMARY KEY,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(platform_id, slug)
);

-- Content tipovi po platformama
INSERT INTO content_types (platform_id, name, slug, description) VALUES
-- Instagram (platform_id = 1)
(1, 'Photo Feed Post', 'photo-feed-post', 'Obična fotografija u Instagram feed-u'),
(1, 'Reel', 'reel', 'Kratki video u Instagram Reels sekciji'),
(1, 'Story', 'story', 'Instagram Story koji traje 24 sata'),
(1, 'Live Stream', 'live-stream', 'Instagram Live prenos'),
(1, 'IGTV', 'igtv', 'Duži video sadržaj na IGTV'),

-- TikTok (platform_id = 2)
(2, 'Video Post', 'video-post', 'Standardni TikTok video'),
(2, 'Live Stream', 'live-stream', 'TikTok Live prenos'),

-- YouTube (platform_id = 3)
(3, 'Short Video', 'short-video', 'YouTube video kraći od 1 minute'),
(3, 'Long Video', 'long-video', 'YouTube video duži od 1 minute'),
(3, 'YouTube Shorts', 'youtube-shorts', 'Kratki vertikalni video'),
(3, 'Live Stream', 'live-stream', 'YouTube Live prenos');
```

#### **Nova tabela: influencer_platform_pricing**
```sql
CREATE TABLE influencer_platform_pricing (
  id SERIAL PRIMARY KEY,
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  content_type_id INTEGER REFERENCES content_types(id) ON DELETE CASCADE,
  price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'KM',
  is_available BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(influencer_id, platform_id, content_type_id)
);
```

#### **Nova tabela: influencer_platforms** (koje platforme koristi)
```sql
CREATE TABLE influencer_platforms (
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  handle VARCHAR(100),
  followers_count INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  PRIMARY KEY (influencer_id, platform_id)
);
```

### **UKLANJANJE STARIH KOLONA:**
```sql
-- Ukloniti stare kolone iz influencers tabele
ALTER TABLE influencers
DROP COLUMN IF EXISTS instagram_handle,
DROP COLUMN IF EXISTS instagram_followers,
DROP COLUMN IF EXISTS tiktok_handle,
DROP COLUMN IF EXISTS tiktok_followers,
DROP COLUMN IF EXISTS youtube_handle,
DROP COLUMN IF EXISTS youtube_subscribers,
DROP COLUMN IF EXISTS price_per_post,
DROP COLUMN IF EXISTS price_per_story,
DROP COLUMN IF EXISTS price_per_reel;
```

### **POSTOJEĆE TABELE:**

### **Nova tabela: categories**
```sql
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  icon VARCHAR(50),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### **Nova tabela: influencer_categories**
```sql
CREATE TABLE influencer_categories (
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (influencer_id, category_id)
);
```

### **Nova tabela: business_target_categories**
```sql
CREATE TABLE business_target_categories (
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  PRIMARY KEY (business_id, category_id)
);
```

### **Ažuriranje influencers tabele:**
```sql
ALTER TABLE influencers ADD COLUMN gender VARCHAR(20);
ALTER TABLE influencers ADD COLUMN age INTEGER;
ALTER TABLE influencers ADD COLUMN instagram_engagement_rate DECIMAL(5,2);
ALTER TABLE influencers ADD COLUMN instagram_avg_views INTEGER;
ALTER TABLE influencers ADD COLUMN tiktok_engagement_rate DECIMAL(5,2);
ALTER TABLE influencers ADD COLUMN tiktok_avg_views INTEGER;
ALTER TABLE influencers ADD COLUMN youtube_avg_views INTEGER;
ALTER TABLE influencers ADD COLUMN instagram_content_types TEXT[];
ALTER TABLE influencers ADD COLUMN tiktok_content_types TEXT[];
ALTER TABLE influencers ADD COLUMN youtube_content_types TEXT[];
```

### **Ažuriranje businesses tabele:**
```sql
ALTER TABLE businesses ADD COLUMN target_age_min INTEGER;
ALTER TABLE businesses ADD COLUMN target_age_max INTEGER;
ALTER TABLE businesses ADD COLUMN target_gender VARCHAR(20);
ALTER TABLE businesses ADD COLUMN target_location_type VARCHAR(50);
ALTER TABLE businesses ADD COLUMN preferred_follower_min INTEGER;
ALTER TABLE businesses ADD COLUMN preferred_follower_max INTEGER;
ALTER TABLE businesses ADD COLUMN campaign_frequency VARCHAR(50);
```

---

## 🎯 **MATCHING ALGORITAM**

### **Faktori za preporuke:**
1. **Category match** (70% weight)
2. **Location proximity** (15% weight)
3. **Budget compatibility** (10% weight)
4. **Follower range match** (5% weight)

### **Scoring sistem:**
- Perfect category match: 100 points
- Partial category match: 50 points
- Same city: 100 points
- Same country: 50 points
- Budget within range: 100 points
- Follower count match: 50 points

---

## 📋 **IMPLEMENTACIJA PLAN**

### **Korak 1: Database**
1. Kreirati categories tabelu sa predefinisanim kategorijama
2. Ažurirati influencers i businesses tabele
3. Kreirati junction tabele za many-to-many veze

### **Korak 2: Backend**
1. Ažurirati TypeScript tipove
2. Kreirati funkcije za rad sa kategorijama
3. Ažurirati profil funkcije

### **Korak 3: Frontend**
1. Ažurirati influencer profil formu
2. Ažurirati biznis profil formu
3. Kreirati category selector komponente

### **Korak 4: Filter sistem**
1. Kreirati influencer marketplace stranicu
2. Implementirati filter komponente
3. Kreirati search i sort funkcionalnost
