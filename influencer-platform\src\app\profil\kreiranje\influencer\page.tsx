'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Loader2, Upload } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { upsertProfile, createInfluencer, updateInfluencerCategories } from '@/lib/profiles';
import { CategorySelector } from '@/components/ui/category-selector';

const influencerSchema = z.object({
  username: z.string()
    .min(3, 'Username mora imati najmanje 3 karaktera')
    .max(20, 'Username može imati maksimalno 20 karaktera')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username može sadržavati samo slova, brojeve i _'),
  bio: z.string().max(500, 'Bio može imati maksimalno 500 karaktera').optional(),
  location: z.string().max(100, 'Lokacija može imati maksimalno 100 karaktera').optional(),
  website: z.string().url('Unesite validnu URL adresu').optional().or(z.literal('')),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say'], {
    errorMap: () => ({ message: 'Izaberite pol' })
  }).optional(),
  age: z.number()
    .min(13, 'Morate imati najmanje 13 godina')
    .max(100, 'Unesite validnu godinu')
    .optional(),
  instagramHandle: z.string().optional(),
  instagramFollowers: z.number().min(0, 'Broj pratilaca mora biti pozitivan').optional(),
  tiktokHandle: z.string().optional(),
  tiktokFollowers: z.number().min(0, 'Broj pratilaca mora biti pozitivan').optional(),
  youtubeHandle: z.string().optional(),
  youtubeSubscribers: z.number().min(0, 'Broj pretplatnika mora biti pozitivan').optional(),
  pricePerPost: z.number().min(0, 'Cijena mora biti pozitivna').optional(),
  pricePerStory: z.number().min(0, 'Cijena mora biti pozitivna').optional(),
  pricePerReel: z.number().min(0, 'Cijena mora biti pozitivna').optional(),
});

type InfluencerForm = z.infer<typeof influencerSchema>;

export default function InfluencerProfilPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<InfluencerForm>({
    resolver: zodResolver(influencerSchema),
  });

  const onSubmit = async (data: InfluencerForm) => {
    if (!user) return;

    // Validate categories
    if (selectedCategories.length === 0) {
      setError('root', { message: 'Morate izabrati najmanje jednu kategoriju' });
      return;
    }

    setIsLoading(true);

    try {
      // Create or update basic profile
      const { error: profileError } = await upsertProfile(user.id, {
        username: data.username,
        bio: data.bio || null,
        location: data.location || null,
        website_url: data.website || null,
        user_type: 'influencer',
      });

      if (profileError) {
        if (profileError.message.includes('username')) {
          setError('username', { message: 'Username je već zauzet' });
        } else {
          setError('root', { message: 'Greška pri ažuriranju profila' });
        }
        return;
      }

      // Create influencer specific data
      const { error: influencerError } = await createInfluencer({
        id: user.id,
        instagram_handle: data.instagramHandle || null,
        instagram_followers: data.instagramFollowers || 0,
        tiktok_handle: data.tiktokHandle || null,
        tiktok_followers: data.tiktokFollowers || 0,
        youtube_handle: data.youtubeHandle || null,
        youtube_subscribers: data.youtubeSubscribers || 0,
        gender: data.gender || null,
        age: data.age || null,
        price_per_post: data.pricePerPost || null,
        price_per_story: data.pricePerStory || null,
        price_per_reel: data.pricePerReel || null,
      });

      if (influencerError) {
        setError('root', { message: 'Greška pri kreiranju influencer profila' });
        return;
      }

      // Update categories
      const { error: categoriesError } = await updateInfluencerCategories(user.id, selectedCategories);
      if (categoriesError) {
        setError('root', { message: 'Greška pri ažuriranju kategorija' });
        return;
      }

      // Redirect to dashboard
      router.push('/dashboard/influencer');
    } catch (error) {
      setError('root', { message: 'Neočekivana greška. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/profil/kreiranje" className="flex items-center space-x-2 text-muted-foreground hover:text-foreground">
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-foreground">InfluConnect</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-8">
        <div className="container mx-auto max-w-2xl">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Influencer Profil</CardTitle>
              <CardDescription>
                Popunite informacije o sebi da biste završili svoj profil
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Categories */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Kategorije</h3>
                  <p className="text-sm text-muted-foreground">
                    Izaberite kategorije u kojima kreirate sadržaj (minimum 1, maksimum 3)
                  </p>
                  <CategorySelector
                    selectedCategories={selectedCategories}
                    onCategoriesChange={setSelectedCategories}
                    maxCategories={3}
                    placeholder="Izaberite vaše kategorije..."
                  />
                </div>

                {/* Basic Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Osnovne informacije</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="username">Username *</Label>
                    <Input
                      id="username"
                      placeholder="marko_markovic"
                      {...register('username')}
                      className={errors.username ? 'border-red-500' : ''}
                    />
                    {errors.username && (
                      <p className="text-sm text-red-500">{errors.username.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      placeholder="Opišite sebe u nekoliko rečenica..."
                      rows={3}
                      {...register('bio')}
                      className={errors.bio ? 'border-red-500' : ''}
                    />
                    {errors.bio && (
                      <p className="text-sm text-red-500">{errors.bio.message}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location">Lokacija</Label>
                      <Input
                        id="location"
                        placeholder="Sarajevo, BiH"
                        {...register('location')}
                        className={errors.location ? 'border-red-500' : ''}
                      />
                      {errors.location && (
                        <p className="text-sm text-red-500">{errors.location.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        placeholder="https://vaswebsite.com"
                        {...register('website')}
                        className={errors.website ? 'border-red-500' : ''}
                      />
                      {errors.website && (
                        <p className="text-sm text-red-500">{errors.website.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gender">Pol</Label>
                      <select
                        id="gender"
                        {...register('gender')}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Izaberite...</option>
                        <option value="male">Muški</option>
                        <option value="female">Ženski</option>
                        <option value="other">Ostalo</option>
                        <option value="prefer_not_to_say">Preferiram da ne kažem</option>
                      </select>
                      {errors.gender && (
                        <p className="text-sm text-red-500">{errors.gender.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="age">Godine</Label>
                      <Input
                        id="age"
                        type="number"
                        placeholder="25"
                        min="13"
                        max="100"
                        {...register('age', { valueAsNumber: true })}
                        className={errors.age ? 'border-red-500' : ''}
                      />
                      {errors.age && (
                        <p className="text-sm text-red-500">{errors.age.message}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Social Media */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Društvene mreže</h3>
                  
                  {/* Instagram */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="instagramHandle">Instagram handle</Label>
                      <Input
                        id="instagramHandle"
                        placeholder="marko_markovic"
                        {...register('instagramHandle')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="instagramFollowers">Broj pratilaca</Label>
                      <Input
                        id="instagramFollowers"
                        type="number"
                        placeholder="1000"
                        {...register('instagramFollowers', { valueAsNumber: true })}
                      />
                    </div>
                  </div>

                  {/* TikTok */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="tiktokHandle">TikTok handle</Label>
                      <Input
                        id="tiktokHandle"
                        placeholder="marko_markovic"
                        {...register('tiktokHandle')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="tiktokFollowers">Broj pratilaca</Label>
                      <Input
                        id="tiktokFollowers"
                        type="number"
                        placeholder="500"
                        {...register('tiktokFollowers', { valueAsNumber: true })}
                      />
                    </div>
                  </div>

                  {/* YouTube */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="youtubeHandle">YouTube kanal</Label>
                      <Input
                        id="youtubeHandle"
                        placeholder="MarkoMarkovic"
                        {...register('youtubeHandle')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="youtubeSubscribers">Broj pretplatnika</Label>
                      <Input
                        id="youtubeSubscribers"
                        type="number"
                        placeholder="200"
                        {...register('youtubeSubscribers', { valueAsNumber: true })}
                      />
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Cijene</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="pricePerPost">Cijena po postu (KM)</Label>
                      <Input
                        id="pricePerPost"
                        type="number"
                        placeholder="100"
                        {...register('pricePerPost', { valueAsNumber: true })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pricePerStory">Cijena po story (KM)</Label>
                      <Input
                        id="pricePerStory"
                        type="number"
                        placeholder="50"
                        {...register('pricePerStory', { valueAsNumber: true })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pricePerReel">Cijena po reel (KM)</Label>
                      <Input
                        id="pricePerReel"
                        type="number"
                        placeholder="150"
                        {...register('pricePerReel', { valueAsNumber: true })}
                      />
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kreiranje profila...
                    </>
                  ) : (
                    'Završi profil'
                  )}
                </Button>

                {/* Root Error */}
                {errors.root && (
                  <p className="text-sm text-red-500 text-center">{errors.root.message}</p>
                )}
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
