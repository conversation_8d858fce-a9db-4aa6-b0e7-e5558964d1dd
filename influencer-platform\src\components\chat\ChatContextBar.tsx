'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ExternalLink, 
  Calendar, 
  DollarSign, 
  Target,
  Building2,
  User
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';

interface ChatContextBarProps {
  campaignApplicationId?: string | null;
  offerId?: string | null;
}

interface CampaignData {
  id: string;
  title: string;
  budget: number;
  deadline: string | null;
  status: string;
  company_name: string;
}

interface OfferData {
  id: string;
  title: string;
  budget: number;
  deadline: string | null;
  status: string;
  influencer_name: string;
  business_name: string;
}

export function ChatContextBar({ campaignApplicationId, offerId }: ChatContextBarProps) {
  const { user } = useAuth();
  const [campaignData, setCampaignData] = useState<CampaignData | null>(null);
  const [offerData, setOfferData] = useState<OfferData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContextData();
  }, [campaignApplicationId, offerId]);

  const loadContextData = async () => {
    setLoading(true);
    try {
      if (campaignApplicationId) {
        await loadCampaignData();
      } else if (offerId) {
        await loadOfferData();
      }
    } catch (error) {
      console.error('Error loading context data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCampaignData = async () => {
    if (!campaignApplicationId) return;

    try {
      const { data: application, error } = await supabase
        .from('campaign_applications')
        .select(`
          id,
          status,
          campaigns (
            id,
            title,
            budget,
            deadline,
            status,
            businesses (
              company_name
            )
          )
        `)
        .eq('id', campaignApplicationId)
        .single();

      if (error) {
        console.error('Error loading campaign data:', error);
        return;
      }

      if (application?.campaigns) {
        setCampaignData({
          id: application.campaigns.id,
          title: application.campaigns.title,
          budget: application.campaigns.budget,
          deadline: application.campaigns.deadline,
          status: application.status,
          company_name: application.campaigns.businesses?.company_name || 'Nepoznato'
        });
      }
    } catch (error) {
      console.error('Error loading campaign data:', error);
    }
  };

  const loadOfferData = async () => {
    if (!offerId) return;

    try {
      // Get offer data with business info
      const { data: offer, error } = await supabase
        .from('direct_offers')
        .select(`
          id,
          title,
          budget,
          deadline,
          status,
          influencer_id,
          businesses (
            company_name
          )
        `)
        .eq('id', offerId)
        .single();

      if (error) {
        console.error('Error loading offer data:', error);
        return;
      }

      // Get influencer profile separately
      const { data: influencerProfile } = await supabase
        .from('profiles')
        .select('username, full_name')
        .eq('id', offer.influencer_id)
        .single();

      if (offer) {
        setOfferData({
          id: offer.id,
          title: offer.title,
          budget: offer.budget,
          deadline: offer.deadline,
          status: offer.status || 'pending',
          business_name: offer.businesses?.company_name || 'Nepoznato',
          influencer_name: influencerProfile?.full_name || influencerProfile?.username || 'Nepoznato'
        });
      }
    } catch (error) {
      console.error('Error loading offer data:', error);
    }
  };

  const getContextLink = () => {
    if (!user) return '#';
    
    const userType = user.user_metadata?.user_type || 'influencer';
    
    if (campaignApplicationId) {
      if (userType === 'business') {
        return `/dashboard/biznis/applications/${campaignApplicationId}`;
      } else {
        return `/dashboard/influencer/applications/${campaignApplicationId}`;
      }
    } else if (offerId) {
      if (userType === 'business') {
        return `/dashboard/biznis/offers/${offerId}`;
      } else {
        return `/dashboard/influencer/offers/${offerId}`;
      }
    }
    
    return '#';
  };

  const getContextTitle = () => {
    if (campaignData) return campaignData.title;
    if (offerData) return offerData.title;
    if (campaignApplicationId) return 'Kampanja';
    if (offerId) return 'Direktna ponuda';
    return 'Razgovor';
  };

  const getContextType = () => {
    if (campaignApplicationId) return 'Kampanja';
    if (offerId) return 'Direktna ponuda';
    return 'Razgovor';
  };

  const getBudget = () => {
    if (campaignData) return campaignData.budget;
    if (offerData) return offerData.budget;
    return null;
  };

  const getDeadline = () => {
    if (campaignData) return campaignData.deadline;
    if (offerData) return offerData.deadline;
    return null;
  };

  const getStatus = () => {
    if (campaignData) return campaignData.status;
    if (offerData) return offerData.status;
    return null;
  };

  const getStatusBadge = (status: string | null) => {
    if (!status) return null;
    
    switch (status) {
      case 'active':
      case 'accepted':
        return <Badge variant="default" className="bg-green-500">{status === 'active' ? 'Aktivna' : 'Prihvaćeno'}</Badge>;
      case 'pending':
        return <Badge variant="secondary">Na čekanju</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Odbačeno</Badge>;
      case 'completed':
        return <Badge variant="outline" className="border-green-500 text-green-700">Završeno</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card className="mb-4">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-900"></div>
              <span className="text-sm text-muted-foreground">Učitava kontekst...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-4 border-l-4 border-l-primary">
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {campaignApplicationId ? (
                <Target className="h-4 w-4 text-primary" />
              ) : (
                <Building2 className="h-4 w-4 text-primary" />
              )}
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{getContextTitle()}</span>
                  {getStatusBadge(getStatus())}
                </div>
                <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                  <span className="flex items-center gap-1">
                    <span className="font-medium">{getContextType()}</span>
                  </span>
                  {getBudget() && (
                    <span className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      {getBudget()?.toLocaleString()} KM
                    </span>
                  )}
                  {getDeadline() && (
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(getDeadline()!).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <Button variant="outline" size="sm" asChild>
            <Link href={getContextLink()}>
              <ExternalLink className="h-3 w-3 mr-1" />
              Detalji
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
