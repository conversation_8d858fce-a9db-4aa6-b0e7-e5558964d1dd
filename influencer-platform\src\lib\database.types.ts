export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      business_target_categories: {
        Row: {
          business_id: string
          category_id: number
          created_at: string | null
        }
        Insert: {
          business_id: string
          category_id: number
          created_at?: string | null
        }
        Update: {
          business_id?: string
          category_id?: number
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "business_target_categories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_target_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      businesses: {
        Row: {
          budget_range: string | null
          company_name: string
          company_size: string | null
          created_at: string | null
          id: string
          industry: string | null
          is_verified: boolean | null
          updated_at: string | null
        }
        Insert: {
          budget_range?: string | null
          company_name: string
          company_size?: string | null
          created_at?: string | null
          id: string
          industry?: string | null
          is_verified?: boolean | null
          updated_at?: string | null
        }
        Update: {
          budget_range?: string | null
          company_name?: string
          company_size?: string | null
          created_at?: string | null
          id?: string
          industry?: string | null
          is_verified?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_applications: {
        Row: {
          additional_services: string | null
          applied_at: string | null
          audience_insights: string | null
          available_start_date: string | null
          campaign_id: string
          delivery_timeframe: string | null
          experience_relevant: string | null
          id: string
          influencer_id: string
          portfolio_links: string[] | null
          proposal_text: string | null
          proposed_rate: number
          responded_at: string | null
          status: Database["public"]["Enums"]["application_status"] | null
        }
        Insert: {
          additional_services?: string | null
          applied_at?: string | null
          audience_insights?: string | null
          available_start_date?: string | null
          campaign_id: string
          delivery_timeframe?: string | null
          experience_relevant?: string | null
          id?: string
          influencer_id: string
          portfolio_links?: string[] | null
          proposal_text?: string | null
          proposed_rate: number
          responded_at?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
        }
        Update: {
          additional_services?: string | null
          applied_at?: string | null
          audience_insights?: string | null
          available_start_date?: string | null
          campaign_id?: string
          delivery_timeframe?: string | null
          experience_relevant?: string | null
          id?: string
          influencer_id?: string
          portfolio_links?: string[] | null
          proposal_text?: string | null
          proposed_rate?: number
          responded_at?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_applications_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      campaigns: {
        Row: {
          budget: number | null
          business_id: string
          campaign_goal: string | null
          content_types: Database["public"]["Enums"]["content_type"][]
          created_at: string | null
          deliverables: string | null
          description: string
          end_date: string | null
          id: string
          product_description: string | null
          requirements: string | null
          show_business_name: boolean | null
          start_date: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          target_audience: Json | null
          title: string
          updated_at: string | null
        }
        Insert: {
          budget?: number | null
          business_id: string
          campaign_goal?: string | null
          content_types: Database["public"]["Enums"]["content_type"][]
          created_at?: string | null
          deliverables?: string | null
          description: string
          end_date?: string | null
          id?: string
          product_description?: string | null
          requirements?: string | null
          show_business_name?: boolean | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_audience?: Json | null
          title: string
          updated_at?: string | null
        }
        Update: {
          budget?: number | null
          business_id?: string
          campaign_goal?: string | null
          content_types?: Database["public"]["Enums"]["content_type"][]
          created_at?: string | null
          deliverables?: string | null
          description?: string
          end_date?: string | null
          id?: string
          product_description?: string | null
          requirements?: string | null
          show_business_name?: boolean | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_audience?: Json | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          icon: string | null
          id: number
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name?: string
          slug?: string
        }
        Relationships: []
      }
      chat_messages: {
        Row: {
          created_at: string | null
          edited_at: string | null
          file_name: string | null
          file_size: number | null
          file_type: string | null
          file_url: string | null
          id: string
          message_text: string | null
          read_at: string | null
          room_id: string
          sender_id: string
          sender_type: string
        }
        Insert: {
          created_at?: string | null
          edited_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_type?: string | null
          file_url?: string | null
          id?: string
          message_text?: string | null
          read_at?: string | null
          room_id: string
          sender_id: string
          sender_type: string
        }
        Update: {
          created_at?: string | null
          edited_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_type?: string | null
          file_url?: string | null
          id?: string
          message_text?: string | null
          read_at?: string | null
          room_id?: string
          sender_id?: string
          sender_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_participants: {
        Row: {
          id: string
          is_active: boolean | null
          joined_at: string | null
          last_read_at: string | null
          room_id: string
          user_id: string
          user_type: string
        }
        Insert: {
          id?: string
          is_active?: boolean | null
          joined_at?: string | null
          last_read_at?: string | null
          room_id: string
          user_id: string
          user_type: string
        }
        Update: {
          id?: string
          is_active?: boolean | null
          joined_at?: string | null
          last_read_at?: string | null
          room_id?: string
          user_id?: string
          user_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_participants_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_permissions: {
        Row: {
          business_approved: boolean | null
          business_id: string
          campaign_application_id: string | null
          chat_enabled: boolean | null
          created_at: string | null
          id: string
          influencer_approved: boolean | null
          influencer_id: string
          offer_id: string | null
          updated_at: string | null
        }
        Insert: {
          business_approved?: boolean | null
          business_id: string
          campaign_application_id?: string | null
          chat_enabled?: boolean | null
          created_at?: string | null
          id?: string
          influencer_approved?: boolean | null
          influencer_id: string
          offer_id?: string | null
          updated_at?: string | null
        }
        Update: {
          business_approved?: boolean | null
          business_id?: string
          campaign_application_id?: string | null
          chat_enabled?: boolean | null
          created_at?: string | null
          id?: string
          influencer_approved?: boolean | null
          influencer_id?: string
          offer_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_permissions_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_permissions_campaign_application_id_fkey"
            columns: ["campaign_application_id"]
            isOneToOne: false
            referencedRelation: "campaign_applications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_permissions_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_permissions_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_permissions_offer_id_fkey"
            columns: ["offer_id"]
            isOneToOne: false
            referencedRelation: "direct_offers"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_rooms: {
        Row: {
          business_id: string
          campaign_application_id: string | null
          created_at: string | null
          id: string
          influencer_id: string
          last_message_at: string | null
          offer_id: string | null
          room_title: string
          room_type: string
          updated_at: string | null
        }
        Insert: {
          business_id: string
          campaign_application_id?: string | null
          created_at?: string | null
          id?: string
          influencer_id: string
          last_message_at?: string | null
          offer_id?: string | null
          room_title: string
          room_type: string
          updated_at?: string | null
        }
        Update: {
          business_id?: string
          campaign_application_id?: string | null
          created_at?: string | null
          id?: string
          influencer_id?: string
          last_message_at?: string | null
          offer_id?: string | null
          room_title?: string
          room_type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_rooms_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_rooms_campaign_application_id_fkey"
            columns: ["campaign_application_id"]
            isOneToOne: false
            referencedRelation: "campaign_applications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_rooms_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_rooms_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_rooms_offer_id_fkey"
            columns: ["offer_id"]
            isOneToOne: false
            referencedRelation: "direct_offers"
            referencedColumns: ["id"]
          },
        ]
      }
      direct_offers: {
        Row: {
          budget: number
          business_id: string
          content_types: string[]
          created_at: string | null
          deadline: string | null
          description: string
          id: string
          influencer_id: string
          platforms: string[]
          requirements: string | null
          responded_at: string | null
          status: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          budget: number
          business_id: string
          content_types: string[]
          created_at?: string | null
          deadline?: string | null
          description: string
          id?: string
          influencer_id: string
          platforms: string[]
          requirements?: string | null
          responded_at?: string | null
          status?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          budget?: number
          business_id?: string
          content_types?: string[]
          created_at?: string | null
          deadline?: string | null
          description?: string
          id?: string
          influencer_id?: string
          platforms?: string[]
          requirements?: string | null
          responded_at?: string | null
          status?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "direct_offers_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "direct_offers_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "direct_offers_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      influencers: {
        Row: {
          age: number | null
          created_at: string | null
          engagement_rate: number | null
          gender: string | null
          id: string
          instagram_followers: number | null
          instagram_handle: string | null
          is_verified: boolean | null
          portfolio_urls: string[] | null
          price_per_post: number | null
          price_per_reel: number | null
          price_per_story: number | null
          tiktok_followers: number | null
          tiktok_handle: string | null
          updated_at: string | null
          youtube_handle: string | null
          youtube_subscribers: number | null
        }
        Insert: {
          age?: number | null
          created_at?: string | null
          engagement_rate?: number | null
          gender?: string | null
          id: string
          instagram_followers?: number | null
          instagram_handle?: string | null
          is_verified?: boolean | null
          portfolio_urls?: string[] | null
          price_per_post?: number | null
          price_per_reel?: number | null
          price_per_story?: number | null
          tiktok_followers?: number | null
          tiktok_handle?: string | null
          updated_at?: string | null
          youtube_handle?: string | null
          youtube_subscribers?: number | null
        }
        Update: {
          age?: number | null
          created_at?: string | null
          engagement_rate?: number | null
          gender?: string | null
          id?: string
          instagram_followers?: number | null
          instagram_handle?: string | null
          is_verified?: boolean | null
          portfolio_urls?: string[] | null
          price_per_post?: number | null
          price_per_reel?: number | null
          price_per_story?: number | null
          tiktok_followers?: number | null
          tiktok_handle?: string | null
          updated_at?: string | null
          youtube_handle?: string | null
          youtube_subscribers?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "influencers_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          bank_account: string | null
          bank_name: string | null
          bio: string | null
          city: string | null
          country: string | null
          created_at: string | null
          full_name: string | null
          id: string
          location: string | null
          phone: string | null
          postal_code: string | null
          tax_id: string | null
          updated_at: string | null
          user_type: Database["public"]["Enums"]["user_type"]
          username: string | null
          website_url: string | null
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          bank_account?: string | null
          bank_name?: string | null
          bio?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          full_name?: string | null
          id: string
          location?: string | null
          phone?: string | null
          postal_code?: string | null
          tax_id?: string | null
          updated_at?: string | null
          user_type: Database["public"]["Enums"]["user_type"]
          username?: string | null
          website_url?: string | null
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          bank_account?: string | null
          bank_name?: string | null
          bio?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          full_name?: string | null
          id?: string
          location?: string | null
          phone?: string | null
          postal_code?: string | null
          tax_id?: string | null
          updated_at?: string | null
          user_type?: Database["public"]["Enums"]["user_type"]
          username?: string | null
          website_url?: string | null
        }
        Relationships: []
      }
      influencer_categories: {
        Row: {
          category_id: number
          created_at: string | null
          id: string
          influencer_id: string
          is_primary: boolean | null
        }
        Insert: {
          category_id: number
          created_at?: string | null
          id?: string
          influencer_id: string
          is_primary?: boolean | null
        }
        Update: {
          category_id?: number
          created_at?: string | null
          id?: string
          influencer_id?: string
          is_primary?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "influencer_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_categories_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      influencer_platforms: {
        Row: {
          content_type_ids: number[] | null
          created_at: string | null
          engagement_rate: number | null
          followers_count: number | null
          handle: string | null
          id: string
          influencer_id: string
          is_active: boolean | null
          is_verified: boolean | null
          platform_id: number
          price_per_post: number | null
          profile_url: string | null
          updated_at: string | null
        }
        Insert: {
          content_type_ids?: number[] | null
          created_at?: string | null
          engagement_rate?: number | null
          followers_count?: number | null
          handle?: string | null
          id?: string
          influencer_id: string
          is_active?: boolean | null
          is_verified?: boolean | null
          platform_id: number
          price_per_post?: number | null
          profile_url?: string | null
          updated_at?: string | null
        }
        Update: {
          content_type_ids?: number[] | null
          created_at?: string | null
          engagement_rate?: number | null
          followers_count?: number | null
          handle?: string | null
          id?: string
          influencer_id?: string
          is_active?: boolean | null
          is_verified?: boolean | null
          platform_id?: number
          price_per_post?: number | null
          profile_url?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "influencer_platforms_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platforms_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      influencer_platform_pricing: {
        Row: {
          content_type_id: number
          created_at: string | null
          currency: string | null
          id: string
          influencer_id: string
          is_available: boolean | null
          platform_id: number
          price: number
          updated_at: string | null
        }
        Insert: {
          content_type_id: number
          created_at?: string | null
          currency?: string | null
          id?: string
          influencer_id: string
          is_available?: boolean | null
          platform_id: number
          price: number
          updated_at?: string | null
        }
        Update: {
          content_type_id?: number
          created_at?: string | null
          currency?: string | null
          id?: string
          influencer_id?: string
          is_available?: boolean | null
          platform_id?: number
          price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "influencer_platform_pricing_content_type_id_fkey"
            columns: ["content_type_id"]
            isOneToOne: false
            referencedRelation: "content_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platform_pricing_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platform_pricing_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      platforms: {
        Row: {
          created_at: string | null
          icon: string | null
          id: number
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          icon?: string | null
          id?: number
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          icon?: string | null
          id?: number
          name?: string
          slug?: string
        }
        Relationships: []
      }
      content_types: {
        Row: {
          created_at: string | null
          icon: string | null
          id: number
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          icon?: string | null
          id?: number
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          icon?: string | null
          id?: number
          name?: string
          slug?: string
        }
        Relationships: []
      }
    }
    Views: {
      campaigns_with_details: {
        Row: {
          budget: number | null
          business_id: string
          campaign_goal: string | null
          content_types: Database["public"]["Enums"]["content_type"][] | null
          created_at: string | null
          deliverables: string | null
          description: string | null
          end_date: string | null
          id: string | null
          product_description: string | null
          requirements: string | null
          show_business_name: boolean | null
          start_date: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          target_audience: Json | null
          title: string | null
          updated_at: string | null
        }
        Relationships: []
      }
      influencer_search_view: {
        Row: {
          age: number | null
          avatar_url: string | null
          bio: string | null
          categories: string[] | null
          city: string | null
          country: string | null
          created_at: string | null
          engagement_rate: number | null
          full_name: string | null
          gender: string | null
          id: string | null
          instagram_followers: number | null
          instagram_handle: string | null
          is_verified: boolean | null
          location: string | null
          portfolio_urls: string[] | null
          price_per_post: number | null
          price_per_reel: number | null
          price_per_story: number | null
          tiktok_followers: number | null
          tiktok_handle: string | null
          updated_at: string | null
          user_type: Database["public"]["Enums"]["user_type"] | null
          username: string | null
          website_url: string | null
          youtube_handle: string | null
          youtube_subscribers: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      get_influencers_with_details: {
        Args: {
          search_query?: string
          category_filter?: string
          platform_filter?: string
          min_followers?: number
          max_followers?: number
          min_price?: number
          max_price?: number
          location_filter?: string
          gender_filter?: string
          age_min?: number
          age_max?: number
          limit_count?: number
          offset_count?: number
        }
        Returns: {
          id: string
          full_name: string
          username: string
          bio: string
          avatar_url: string
          location: string
          city: string
          country: string
          website_url: string
          age: number
          gender: string
          instagram_followers: number
          instagram_handle: string
          tiktok_followers: number
          tiktok_handle: string
          youtube_subscribers: number
          youtube_handle: string
          engagement_rate: number
          price_per_post: number
          price_per_reel: number
          price_per_story: number
          is_verified: boolean
          portfolio_urls: string[]
          categories: string[]
          platforms: Json
        }[]
      }
      upsert_chat_permission: {
        Args: {
          p_business_id: string
          p_influencer_id: string
          p_campaign_application_id?: string
          p_offer_id?: string
          p_business_approved?: boolean
          p_influencer_approved?: boolean
          p_chat_enabled?: boolean
        }
        Returns: {
          id: string
          business_id: string
          influencer_id: string
          campaign_application_id: string
          offer_id: string
          business_approved: boolean
          influencer_approved: boolean
          chat_enabled: boolean
          created_at: string
          updated_at: string
        }
      }
    }
    Enums: {
      application_status: "pending" | "accepted" | "rejected"
      campaign_status: "draft" | "active" | "paused" | "completed" | "cancelled"
      content_type: "post" | "story" | "reel" | "video" | "blog"
      user_type: "influencer" | "business"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
