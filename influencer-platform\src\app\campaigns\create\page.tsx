'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Loader2, Plus } from 'lucide-react';
import Link from 'next/link';
import { CreateCampaignForm } from '@/components/campaigns/create-campaign-form';

export default function CreateCampaignPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Redirect if not authenticated
  if (!authLoading && !user) {
    router.push('/prijava');
    return null;
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link 
            href="/dashboard/biznis" 
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad na dashboard</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-foreground">InfluConnect</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-8">
        <div className="container mx-auto max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Kreiraj novu kampanju
            </h1>
            <p className="text-muted-foreground">
              Popunite informacije o vašoj kampanji da biste privukli odgovarajuće influencere
            </p>
          </div>

          <CreateCampaignForm 
            onSuccess={(campaignId) => {
              router.push(`/campaigns/${campaignId}`);
            }}
            onCancel={() => {
              router.push('/dashboard/biznis');
            }}
          />
        </div>
      </main>
    </div>
  );
}
