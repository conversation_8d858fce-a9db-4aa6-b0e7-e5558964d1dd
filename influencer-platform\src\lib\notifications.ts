import { supabase } from './supabase';

export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  data: Record<string, any>;
  read: boolean;
  created_at: string;
  updated_at: string;
}

export type NotificationType = 
  | 'offer_received'
  | 'offer_accepted'
  | 'offer_rejected'
  | 'campaign_application'
  | 'campaign_accepted'
  | 'campaign_rejected'
  | 'message_received'
  | 'payment_received';

// Create a new notification
export async function createNotification(
  userId: string,
  type: NotificationType,
  title: string,
  message: string,
  data: Record<string, any> = {}
) {
  const { data: notification, error } = await supabase.rpc('create_notification', {
    p_user_id: userId,
    p_type: type,
    p_title: title,
    p_message: message,
    p_data: data
  });

  if (error) {
    console.error('Error creating notification:', error);
    return { data: null, error };
  }

  return { data: notification, error: null };
}

// Get user notifications
export async function getUserNotifications(userId?: string, limit = 50) {
  let query = supabase
    .from('notifications')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit);

  if (userId) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching notifications:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string) {
  const { data, error } = await supabase
    .from('notifications')
    .update({ read: true })
    .eq('id', notificationId)
    .select()
    .single();

  if (error) {
    console.error('Error marking notification as read:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

// Mark all notifications as read for user
export async function markAllNotificationsAsRead(userId: string) {
  const { data, error } = await supabase
    .from('notifications')
    .update({ read: true })
    .eq('user_id', userId)
    .eq('read', false);

  if (error) {
    console.error('Error marking all notifications as read:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

// Get unread notification count
export async function getUnreadNotificationCount(userId: string) {
  const { count, error } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('read', false);

  if (error) {
    console.error('Error getting unread count:', error);
    return { count: 0, error };
  }

  return { count: count || 0, error: null };
}

// Delete notification
export async function deleteNotification(notificationId: string) {
  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', notificationId);

  if (error) {
    console.error('Error deleting notification:', error);
    return { error };
  }

  return { error: null };
}

// Helper functions for specific notification types

export async function notifyOfferReceived(
  influencerId: string,
  businessName: string,
  offerTitle: string,
  offerId: string
) {
  return createNotification(
    influencerId,
    'offer_received',
    'Nova direktna ponuda',
    `${businessName} vam je poslao ponudu: "${offerTitle}"`,
    { offer_id: offerId, business_name: businessName }
  );
}

export async function notifyOfferAccepted(
  businessId: string,
  influencerName: string,
  offerTitle: string,
  offerId: string
) {
  return createNotification(
    businessId,
    'offer_accepted',
    'Ponuda prihvaćena',
    `${influencerName} je prihvatio vašu ponudu: "${offerTitle}"`,
    { offer_id: offerId, influencer_name: influencerName }
  );
}

export async function notifyOfferRejected(
  businessId: string,
  influencerName: string,
  offerTitle: string,
  offerId: string
) {
  return createNotification(
    businessId,
    'offer_rejected',
    'Ponuda odbijena',
    `${influencerName} je odbio vašu ponudu: "${offerTitle}"`,
    { offer_id: offerId, influencer_name: influencerName }
  );
}

export async function notifyCampaignApplication(
  businessId: string,
  influencerName: string,
  campaignTitle: string,
  applicationId: string
) {
  return createNotification(
    businessId,
    'campaign_application',
    'Nova aplikacija na kampanju',
    `${influencerName} se prijavio na kampanju: "${campaignTitle}"`,
    { application_id: applicationId, influencer_name: influencerName }
  );
}

export async function notifyCampaignAccepted(
  influencerId: string,
  businessName: string,
  campaignTitle: string,
  applicationId: string
) {
  return createNotification(
    influencerId,
    'campaign_accepted',
    'Aplikacija prihvaćena',
    `${businessName} je prihvatio vašu aplikaciju za kampanju: "${campaignTitle}"`,
    { application_id: applicationId, business_name: businessName }
  );
}

export async function notifyCampaignRejected(
  influencerId: string,
  businessName: string,
  campaignTitle: string,
  applicationId: string
) {
  return createNotification(
    influencerId,
    'campaign_rejected',
    'Aplikacija odbijena',
    `${businessName} je odbio vašu aplikaciju za kampanju: "${campaignTitle}"`,
    { application_id: applicationId, business_name: businessName }
  );
}

export async function notifyMessageReceived(
  userId: string,
  senderName: string,
  conversationId: string
) {
  return createNotification(
    userId,
    'message_received',
    'Nova poruka',
    `${senderName} vam je poslao novu poruku`,
    { conversation_id: conversationId, sender_name: senderName }
  );
}

export async function notifyPaymentReceived(
  influencerId: string,
  amount: number,
  currency: string,
  campaignTitle: string
) {
  return createNotification(
    influencerId,
    'payment_received',
    'Plaćanje primljeno',
    `Primili ste plaćanje od ${amount} ${currency} za kampanju: "${campaignTitle}"`,
    { amount, currency, campaign_title: campaignTitle }
  );
}
