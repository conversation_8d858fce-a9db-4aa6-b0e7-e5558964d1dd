import { supabase } from './supabase';
import { Database } from './database.types';

type Profile = Database['public']['Tables']['profiles']['Row'];
type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

type Influencer = Database['public']['Tables']['influencers']['Row'];
type InfluencerInsert = Database['public']['Tables']['influencers']['Insert'];
type InfluencerUpdate = Database['public']['Tables']['influencers']['Update'];

type Business = Database['public']['Tables']['businesses']['Row'];
type BusinessInsert = Database['public']['Tables']['businesses']['Insert'];
type BusinessUpdate = Database['public']['Tables']['businesses']['Update'];

// Profile functions
export const getProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  return { data, error };
};

export const updateProfile = async (userId: string, updates: ProfileUpdate) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();

  return { data, error };
};

export const createProfile = async (profileData: ProfileInsert) => {
  const { data, error } = await supabase
    .from('profiles')
    .insert(profileData)
    .select()
    .single();

  return { data, error };
};

// Get public influencer profile by username
export const getPublicInfluencerProfile = async (username: string) => {
  // Use RPC function to get influencer data
  const { data: influencerData, error: influencerError } = await supabase
    .rpc('get_influencers_with_details', {
      search_term: username,
      min_followers: 0,
      max_followers: 999999999,
      min_price: 0,
      max_price: 999999,
      platform_filter: '',
      category_filter: '',
      location_filter: '',
      limit_count: 10
    });

  if (influencerError || !influencerData || influencerData.length === 0) {
    return { data: null, error: influencerError || { message: 'Influencer not found' } };
  }

  // Find the exact username match
  const exactMatch = influencerData.find(item => item.username === username);
  if (!exactMatch) {
    return { data: null, error: { message: 'Influencer not found' } };
  }

  const data = exactMatch;

  // Transform data to match expected structure
  const transformedData = {
    id: data.id,
    username: data.username,
    full_name: data.full_name,
    avatar_url: data.avatar_url,
    bio: data.bio,
    location: data.location,
    created_at: data.created_at,
    gender: data.gender,
    age: data.age,
    is_verified: data.is_verified,
    platforms: [
      ...(data.instagram_followers > 0 ? [{
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: '📷',
        handle: `@${data.username}`,
        followers_count: data.instagram_followers,
        is_verified: data.is_verified
      }] : []),
      ...(data.tiktok_followers > 0 ? [{
        platform_id: 2,
        platform_name: 'TikTok',
        platform_icon: '🎵',
        handle: `@${data.username}`,
        followers_count: data.tiktok_followers,
        is_verified: false
      }] : []),
      ...(data.youtube_subscribers > 0 ? [{
        platform_id: 3,
        platform_name: 'YouTube',
        platform_icon: '📺',
        handle: `@${data.username}`,
        followers_count: data.youtube_subscribers,
        is_verified: false
      }] : [])
    ],
    categories: [], // TODO: Add categories when implemented
    pricing: [
      ...(data.price_per_post ? [{
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: '📷',
        content_type_id: 1,
        content_type_name: 'Post',
        price: Number(data.price_per_post),
        currency: 'KM'
      }] : []),
      ...(data.price_per_story ? [{
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: '📷',
        content_type_id: 2,
        content_type_name: 'Story',
        price: Number(data.price_per_story),
        currency: 'KM'
      }] : []),
      ...(data.price_per_reel ? [{
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: '📷',
        content_type_id: 3,
        content_type_name: 'Reel',
        price: Number(data.price_per_reel),
        currency: 'KM'
      }] : [])
    ],
    portfolio_items: [], // TODO: Add portfolio items when implemented
    total_followers: (data.instagram_followers || 0) +
                    (data.tiktok_followers || 0) +
                    (data.youtube_subscribers || 0),
    min_price: Math.min(
      ...[data.price_per_post, data.price_per_story, data.price_per_reel]
        .filter(Boolean)
        .map(Number)
    ) || 0,
    max_price: Math.max(
      ...[data.price_per_post, data.price_per_story, data.price_per_reel]
        .filter(Boolean)
        .map(Number)
    ) || 0
  };

  return { data: transformedData, error: null };
};

export const upsertProfile = async (userId: string, updates: ProfileUpdate) => {
  // First try to get existing profile
  const { data: existingProfile } = await getProfile(userId);

  if (existingProfile) {
    // Profile exists, update it
    return updateProfile(userId, updates);
  } else {
    // Profile doesn't exist, create it
    const profileData: ProfileInsert = {
      id: userId,
      user_type: updates.user_type || 'influencer',
      username: updates.username || null,
      full_name: updates.full_name || null,
      avatar_url: updates.avatar_url || null,
      bio: updates.bio || null,
      website_url: updates.website_url || null,
      location: updates.location || null,
    };
    return createProfile(profileData);
  }
};

export const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {
  let query = supabase
    .from('profiles')
    .select('id')
    .eq('username', username);
  
  if (excludeUserId) {
    query = query.neq('id', excludeUserId);
  }
  
  const { data, error } = await query;
  
  if (error) return { available: false, error };
  return { available: data.length === 0, error: null };
};

// Influencer functions
export const getInfluencer = async (userId: string) => {
  const { data, error } = await supabase
    .from('influencers')
    .select(`
      *,
      profiles (*)
    `)
    .eq('id', userId)
    .single();
  
  return { data, error };
};

export const createInfluencer = async (influencerData: InfluencerInsert) => {
  const { data, error } = await supabase
    .from('influencers')
    .insert(influencerData)
    .select()
    .single();
  
  return { data, error };
};

export const updateInfluencer = async (userId: string, updates: InfluencerUpdate) => {
  const { data, error } = await supabase
    .from('influencers')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();

  return { data, error };
};

export const getInfluencers = async (filters?: {
  niche?: string;
  minFollowers?: number;
  maxFollowers?: number;
  location?: string;
  limit?: number;
  offset?: number;
}) => {
  let query = supabase
    .from('influencers')
    .select(`
      *,
      profiles (*)
    `);

  if (filters?.niche) {
    query = query.ilike('niche', `%${filters.niche}%`);
  }

  if (filters?.minFollowers) {
    query = query.gte('instagram_followers', filters.minFollowers);
  }

  if (filters?.maxFollowers) {
    query = query.lte('instagram_followers', filters.maxFollowers);
  }

  if (filters?.location) {
    query = query.eq('profiles.location', filters.location);
  }

  if (filters?.limit) {
    query = query.limit(filters.limit);
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
  }

  const { data, error } = await query;
  return { data, error };
};

// Business functions
export const getBusiness = async (userId: string) => {
  const { data, error } = await supabase
    .from('businesses')
    .select(`
      *,
      profiles (*)
    `)
    .eq('id', userId)
    .single();
  
  return { data, error };
};

export const createBusiness = async (businessData: BusinessInsert) => {
  const { data, error } = await supabase
    .from('businesses')
    .insert(businessData)
    .select()
    .single();
  
  return { data, error };
};

export const updateBusiness = async (userId: string, updates: BusinessUpdate) => {
  const { data, error } = await supabase
    .from('businesses')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();

  return { data, error };
};

// Category functions
export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name');

  return { data, error };
};

export const getInfluencerCategories = async (influencerId: string) => {
  const { data, error } = await supabase
    .from('influencer_categories')
    .select(`
      category_id,
      is_primary,
      categories (*)
    `)
    .eq('influencer_id', influencerId);

  return { data, error };
};

export const updateInfluencerCategories = async (influencerId: string, categoryIds: number[]) => {
  // First, delete existing categories
  await supabase
    .from('influencer_categories')
    .delete()
    .eq('influencer_id', influencerId);

  // Then insert new categories
  if (categoryIds.length > 0) {
    const categoryData = categoryIds.map((categoryId, index) => ({
      influencer_id: influencerId,
      category_id: categoryId,
      is_primary: index === 0 // First category is primary
    }));

    const { data, error } = await supabase
      .from('influencer_categories')
      .insert(categoryData)
      .select();

    return { data, error };
  }

  return { data: [], error: null };
};

export const getBusinessTargetCategories = async (businessId: string) => {
  const { data, error } = await supabase
    .from('business_target_categories')
    .select(`
      category_id,
      categories (*)
    `)
    .eq('business_id', businessId);

  return { data, error };
};

export const updateBusinessTargetCategories = async (businessId: string, categoryIds: number[]) => {
  // First, delete existing categories
  await supabase
    .from('business_target_categories')
    .delete()
    .eq('business_id', businessId);

  // Then insert new categories
  if (categoryIds.length > 0) {
    const categoryData = categoryIds.map(categoryId => ({
      business_id: businessId,
      category_id: categoryId
    }));

    const { data, error } = await supabase
      .from('business_target_categories')
      .insert(categoryData)
      .select();

    return { data, error };
  }

  return { data: [], error: null };
};

// Combined profile functions
export const getFullProfile = async (userId: string) => {
  const { data: profile, error: profileError } = await getProfile(userId);
  
  if (profileError || !profile) {
    return { data: null, error: profileError };
  }

  if (profile.user_type === 'influencer') {
    const { data: influencer, error: influencerError } = await getInfluencer(userId);
    return { 
      data: influencer ? { ...profile, influencer } : profile, 
      error: influencerError 
    };
  } else if (profile.user_type === 'business') {
    const { data: business, error: businessError } = await getBusiness(userId);
    return { 
      data: business ? { ...profile, business } : profile, 
      error: businessError 
    };
  }

  return { data: profile, error: null };
};

// Upload avatar function
export const uploadAvatar = async (userId: string, file: File) => {
  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}-${Math.random()}.${fileExt}`;
  const filePath = `avatars/${fileName}`;

  const { error: uploadError } = await supabase.storage
    .from('avatars')
    .upload(filePath, file);

  if (uploadError) {
    return { data: null, error: uploadError };
  }

  const { data } = supabase.storage
    .from('avatars')
    .getPublicUrl(filePath);

  // Update profile with new avatar URL
  const { error: updateError } = await updateProfile(userId, {
    avatar_url: data.publicUrl,
  });

  if (updateError) {
    return { data: null, error: updateError };
  }

  return { data: data.publicUrl, error: null };
};

// Platform and Pricing functions
export const getInfluencerPlatforms = async (influencerId: string) => {
  const { data, error } = await supabase
    .from('influencer_platforms')
    .select(`
      *,
      platforms (*)
    `)
    .eq('influencer_id', influencerId)
    .eq('is_active', true);

  return { data, error };
};

export const getInfluencerPricing = async (influencerId: string) => {
  const { data, error } = await supabase
    .from('influencer_platform_pricing')
    .select(`
      *,
      platforms (*),
      content_types (*)
    `)
    .eq('influencer_id', influencerId)
    .eq('is_available', true);

  return { data, error };
};

export const updateInfluencerPlatforms = async (influencerId: string, platforms: any[]) => {
  // First, delete existing platforms
  await supabase
    .from('influencer_platforms')
    .delete()
    .eq('influencer_id', influencerId);

  // Then insert new platforms
  if (platforms.length > 0) {
    const platformData = platforms.map(platform => ({
      influencer_id: influencerId,
      platform_id: platform.platform_id,
      handle: platform.handle || null,
      followers_count: platform.followers_count || 0,
      is_verified: false,
      is_active: true
    }));

    const { data, error } = await supabase
      .from('influencer_platforms')
      .insert(platformData)
      .select();

    return { data, error };
  }

  return { data: [], error: null };
};

export const updateInfluencerPricing = async (influencerId: string, pricing: any[]) => {
  // First, delete existing pricing
  await supabase
    .from('influencer_platform_pricing')
    .delete()
    .eq('influencer_id', influencerId);

  // Then insert new pricing
  if (pricing.length > 0) {
    const pricingData = pricing.map(price => ({
      influencer_id: influencerId,
      platform_id: price.platform_id,
      content_type_id: price.content_type_id,
      price: price.price,
      currency: 'KM',
      is_available: price.is_available !== false
    }));

    const { data, error } = await supabase
      .from('influencer_platform_pricing')
      .insert(pricingData)
      .select();

    return { data, error };
  }

  return { data: [], error: null };
};
