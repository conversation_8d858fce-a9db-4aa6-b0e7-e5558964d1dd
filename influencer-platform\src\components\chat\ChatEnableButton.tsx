'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Check, Clock, Users } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  getChatPermission, 
  approveBusinessChat, 
  approveInfluencerChat,
  isChatEnabled 
} from '@/lib/chat-permissions';
import {
  enableChatForApplication,
  enableChatForOffer,
  getOrCreateChatRoomForApplication,
  getOrCreateChatRoomForOffer
} from '@/lib/chat';
import { toast } from 'sonner';

interface ChatEnableButtonProps {
  applicationId?: string;
  offerId?: string;
  businessId: string;
  influencerId: string;
  onChatEnabled?: () => void;
}

export function ChatEnableButton({ 
  applicationId, 
  offerId, 
  businessId, 
  influencerId, 
  onChatEnabled 
}: ChatEnableButtonProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [chatEnabled, setChatEnabled] = useState(false);
  const [permission, setPermission] = useState<any>(null);

  const userType = user?.user_metadata?.user_type || 'influencer';
  const isBusiness = userType === 'business';
  const isInfluencer = userType === 'influencer';

  useEffect(() => {
    checkChatStatus();
  }, [applicationId, offerId, businessId, influencerId]);

  const checkChatStatus = async () => {
    try {
      const enabled = await isChatEnabled(businessId, influencerId, offerId, applicationId);
      setChatEnabled(enabled);

      if (!enabled) {
        const perm = await getChatPermission(businessId, influencerId, offerId, applicationId);
        setPermission(perm);
      }
    } catch (error) {
      console.error('Error checking chat status:', error);
    }
  };

  const handleEnableChat = async () => {
    if (!user || loading) return;

    setLoading(true);
    try {
      if (!permission) {
        // Create initial permission and room
        if (applicationId) {
          await enableChatForApplication(applicationId, isBusiness, isInfluencer);
        } else if (offerId) {
          await enableChatForOffer(offerId, isBusiness, isInfluencer);
        }
      } else {
        // Approve existing permission
        if (isBusiness) {
          await approveBusinessChat(businessId, influencerId, offerId, applicationId);
        } else {
          await approveInfluencerChat(businessId, influencerId, offerId, applicationId);
        }
      }

      await checkChatStatus();
      toast.success('Chat je omogućen!');
      onChatEnabled?.();
    } catch (error) {
      console.error('Error enabling chat:', error);
      toast.error('Greška pri omogućavanju chat-a');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChat = async () => {
    try {
      // Get the specific chat room for this application/offer
      let roomResult;
      if (applicationId) {
        roomResult = await getOrCreateChatRoomForApplication(applicationId);
      } else if (offerId) {
        roomResult = await getOrCreateChatRoomForOffer(offerId);
      }

      if (roomResult?.data?.id) {
        // Navigate to chat with the specific room ID as a URL parameter
        window.location.href = `/dashboard/chat?room=${roomResult.data.id}`;
      } else {
        // Fallback to general chat page
        window.location.href = '/dashboard/chat';
      }
    } catch (error) {
      console.error('Error opening chat:', error);
      // Fallback to general chat page
      window.location.href = '/dashboard/chat';
    }
  };

  if (chatEnabled) {
    return (
      <Button onClick={handleOpenChat} className="w-full">
        <MessageCircle className="h-4 w-4 mr-2" />
        Otvori chat
      </Button>
    );
  }

  if (!permission) {
    return (
      <Button 
        onClick={handleEnableChat}
        disabled={loading}
        variant="outline"
        className="w-full"
      >
        <MessageCircle className="h-4 w-4 mr-2" />
        {loading ? 'Omogućava se...' : 'Omogući chat'}
      </Button>
    );
  }

  const canApprove = () => {
    if (isBusiness && !permission.business_approved) return true;
    if (isInfluencer && !permission.influencer_approved) return true;
    return false;
  };

  const getStatusText = () => {
    if (isBusiness) {
      if (!permission.business_approved) {
        return 'Odobrite chat';
      } else if (!permission.influencer_approved) {
        return 'Čeka influencera';
      }
    } else {
      if (!permission.influencer_approved) {
        return 'Odobrite chat';
      } else if (!permission.business_approved) {
        return 'Čeka biznis';
      }
    }
    return 'Čeka odobrenje';
  };

  const getStatusBadge = () => {
    if (canApprove()) {
      return (
        <Badge variant="secondary" className="mb-2">
          <Clock className="h-3 w-3 mr-1" />
          Čeka vaše odobrenje
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="mb-2">
        <Clock className="h-3 w-3 mr-1" />
        Čeka partnera
      </Badge>
    );
  };

  return (
    <div className="space-y-2">
      {getStatusBadge()}
      
      {canApprove() ? (
        <Button 
          onClick={handleEnableChat}
          disabled={loading}
          className="w-full"
        >
          <Check className="h-4 w-4 mr-2" />
          {loading ? 'Odobrava se...' : getStatusText()}
        </Button>
      ) : (
        <Button 
          disabled
          variant="outline"
          className="w-full"
        >
          <Clock className="h-4 w-4 mr-2" />
          {getStatusText()}
        </Button>
      )}
    </div>
  );
}
