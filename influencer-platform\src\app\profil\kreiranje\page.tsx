'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getProfile } from '@/lib/profiles';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, User, Building2 } from 'lucide-react';

export default function ProfilKreiranjePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user) {
      loadProfile();
    }
  }, [user, authLoading, router]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await getProfile(user!.id);

      if (error && !error.message.includes('No rows')) {
        console.error('Error loading profile:', error);
        // Continue to show profile creation options even if there's an error
      }

      if (data) {
        setProfile(data);
        // Profile already exists, redirect to appropriate dashboard
        if (data.user_type === 'influencer') {
          router.push('/dashboard/influencer');
        } else if (data.user_type === 'business') {
          router.push('/dashboard/biznis');
        }
        return;
      }

      // Profile doesn't exist, show profile creation options
      setProfile(null);
    } catch (err) {
      console.error('Unexpected error:', err);
      // Continue to show profile creation options
      setProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const handleUserTypeSelection = (userType: 'influencer' | 'business') => {
    if (userType === 'influencer') {
      router.push('/profil/kreiranje/influencer');
    } else {
      router.push('/profil/kreiranje/biznis');
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Učitavanje...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-foreground">InfluConnect</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-foreground mb-2">Završite svoj profil</h1>
            <p className="text-muted-foreground">
              Izaberite tip profila da biste nastavili
            </p>
          </div>

          <div className="space-y-4">
            {/* Influencer Card */}
            <Card 
              className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
              onClick={() => handleUserTypeSelection('influencer')}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-xl">Influencer</CardTitle>
                <CardDescription>
                  Kreiram sadržaj i zarađujem kroz promociju brendova
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Dodaj društvene mreže i broj pratilaca</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Postavi svoje cijene za sadržaj</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Upload portfolio radova</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Business Card */}
            <Card 
              className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
              onClick={() => handleUserTypeSelection('business')}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building2 className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-xl">Biznis</CardTitle>
                <CardDescription>
                  Kreiram kampanje za promociju svojih proizvoda ili usluga
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Dodaj informacije o firmi</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Postavi budžet za kampanje</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Kreiraj prve kampanje</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Možete promijeniti tip profila kasnije u postavkama
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
