'use client';

import { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon: string;
}

interface CategorySelectorProps {
  selectedCategories: number[];
  onCategoriesChange: (categories: number[]) => void;
  maxCategories?: number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function CategorySelector({
  selectedCategories,
  onCategoriesChange,
  maxCategories = 3,
  placeholder = "Izaberite kategorije...",
  className,
  disabled = false
}: CategorySelectorProps) {
  const [open, setOpen] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error loading categories:', error);
        return;
      }

      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryToggle = (categoryId: number) => {
    if (selectedCategories.includes(categoryId)) {
      // Remove category
      onCategoriesChange(selectedCategories.filter(id => id !== categoryId));
    } else {
      // Add category if under limit
      if (selectedCategories.length < maxCategories) {
        onCategoriesChange([...selectedCategories, categoryId]);
      }
    }
  };

  const handleCategoryRemove = (categoryId: number) => {
    onCategoriesChange(selectedCategories.filter(id => id !== categoryId));
  };

  const getSelectedCategoryNames = () => {
    return categories
      .filter(cat => selectedCategories.includes(cat.id))
      .map(cat => ({ id: cat.id, name: cat.name, icon: cat.icon }));
  };

  const getAvailableCategories = () => {
    return categories.filter(cat => !selectedCategories.includes(cat.id));
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-10 w-full bg-muted animate-pulse rounded-md"></div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Selected Categories Display */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {getSelectedCategoryNames().map((category) => (
            <Badge
              key={category.id}
              variant="secondary"
              className="flex items-center gap-1 pr-1"
            >
              <span className="text-sm">{category.icon}</span>
              <span>{category.name}</span>
              {!disabled && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => handleCategoryRemove(category.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </Badge>
          ))}
        </div>
      )}

      {/* Category Selector */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              selectedCategories.length === 0 && "text-muted-foreground"
            )}
            disabled={disabled || selectedCategories.length >= maxCategories}
          >
            {selectedCategories.length === 0 
              ? placeholder
              : selectedCategories.length >= maxCategories
              ? `Maksimalno ${maxCategories} kategorije`
              : `Dodaj kategoriju (${selectedCategories.length}/${maxCategories})`
            }
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Pretražite kategorije..." />
            <CommandEmpty>Nema rezultata.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {getAvailableCategories().map((category) => (
                <CommandItem
                  key={category.id}
                  value={category.name}
                  onSelect={() => {
                    handleCategoryToggle(category.id);
                    if (selectedCategories.length + 1 >= maxCategories) {
                      setOpen(false);
                    }
                  }}
                  className="flex items-center space-x-2"
                >
                  <span className="text-lg">{category.icon}</span>
                  <div className="flex-1">
                    <div className="font-medium">{category.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {category.description}
                    </div>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      selectedCategories.includes(category.id)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Helper Text */}
      <div className="text-xs text-muted-foreground">
        {selectedCategories.length === 0 && (
          <span>Izaberite najmanje 1 kategoriju, maksimalno {maxCategories}</span>
        )}
        {selectedCategories.length > 0 && selectedCategories.length < maxCategories && (
          <span>Možete dodati još {maxCategories - selectedCategories.length} kategorije</span>
        )}
        {selectedCategories.length >= maxCategories && (
          <span>Dostigli ste maksimalan broj kategorija ({maxCategories})</span>
        )}
      </div>
    </div>
  );
}
