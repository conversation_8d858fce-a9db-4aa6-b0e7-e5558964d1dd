'use client';

import { useState, useEffect } from 'react';
import { ChatList } from './ChatList';
import { ChatRoom } from './ChatRoom';
import { ChatRoom as ChatRoomType, getChatRoom } from '@/lib/chat';

interface ChatProps {
  initialRoomId?: string;
}

export function Chat({ initialRoomId }: ChatProps) {
  const [selectedRoom, setSelectedRoom] = useState<ChatRoomType | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (initialRoomId) {
      loadRoom(initialRoomId);
    }
  }, [initialRoomId]);

  const loadRoom = async (roomId: string) => {
    setLoading(true);
    try {
      const { data: room, error } = await getChatRoom(roomId);
      if (room && !error) {
        setSelectedRoom(room);
      }
    } catch (error) {
      console.error('Error loading room:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectRoom = (room: ChatRoomType) => {
    setSelectedRoom(room);
  };

  const handleBackToList = () => {
    setSelectedRoom(null);
  };

  if (loading) {
    return (
      <div className="h-[600px] w-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p>Učitava chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[600px] w-full">
      {selectedRoom ? (
        <ChatRoom room={selectedRoom} onBack={handleBackToList} />
      ) : (
        <ChatList onSelectRoom={handleSelectRoom} />
      )}
    </div>
  );
}
