import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-foreground">InfluConnect</span>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/prijava">Prija<PERSON></Link>
            </Button>
            <Button asChild>
              <Link href="/registracija">Registracija</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            Povezujemo kreatore sa brendovima
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Prva bosanska platforma za influencer marketing. Transparentno, sigurno i efikasno.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/registracija/influencer">Počni kao influencer</Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6" asChild>
              <Link href="/registracija/business">Kreiraj kampanju</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section className="py-20 px-4 bg-muted/50">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Kako funkcioniše</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">👤</span>
                </div>
                <CardTitle>1. Registruj se</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Kreiraj profil kao influencer ili biznis. Dodaj svoje informacije i preferencije.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🤝</span>
                </div>
                <CardTitle>2. Pronađi saradnju</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Influenceri pronalaze kampanje, biznisi biraju kreatore. Sve transparentno i sigurno.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💰</span>
                </div>
                <CardTitle>3. Zaradi</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Kreiraj sadržaj, ispuni ugovor i automatski primi plaćanje. Jednostavno i sigurno.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* For Influencers */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Za influencere</h2>
              <ul className="space-y-4 text-lg">
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Pronađi kampanje koje odgovaraju tvojoj niši</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Postavi svoje cijene i uslove</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Sigurno plaćanje kroz escrow sistem</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Izgradi svoj portfolio i reputaciju</span>
                </li>
              </ul>
              <Button className="mt-8" size="lg" asChild>
                <Link href="/registracija/influencer">Registruj se kao influencer</Link>
              </Button>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-8 text-center">
              <span className="text-6xl">📱</span>
              <p className="text-muted-foreground mt-4">Kreiraj sadržaj koji voliš i zarađuj</p>
            </div>
          </div>
        </div>
      </section>

      {/* For Businesses */}
      <section className="py-20 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-8 text-center">
              <span className="text-6xl">🏢</span>
              <p className="text-muted-foreground mt-4">Dosegni svoju ciljanu publiku</p>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6">Za biznise</h2>
              <ul className="space-y-4 text-lg">
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Kreiraj kampanje za svaki budžet</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Biraj između stotina lokalnih influencera</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Transparentno praćenje rezultata</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-500">✓</span>
                  <span>Plaćaj samo za uspješno izvršene kampanje</span>
                </li>
              </ul>
              <Button className="mt-8" size="lg" asChild>
                <Link href="/registracija/business">Kreiraj prvu kampanju</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border py-12 px-4">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold">InfluConnect</span>
          </div>
          <p className="text-muted-foreground mb-4">
            Povezujemo. Kreiramo. Uspijevamo.
          </p>
          <p className="text-sm text-muted-foreground">
            © 2025 InfluConnect. Sva prava zadržana.
          </p>
        </div>
      </footer>
    </div>
  );
}
