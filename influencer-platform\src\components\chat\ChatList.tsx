'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MessageCircle, Users } from 'lucide-react';
import { ChatRoom as ChatRoomType } from '@/lib/chat';
import { getUserChatRooms } from '@/lib/chat';
import { useAuth } from '@/contexts/AuthContext';

interface ChatListProps {
  onSelectRoom: (room: ChatRoomType) => void;
}

export function ChatList({ onSelectRoom }: ChatListProps) {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<ChatRoomType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadChatRooms();
    }
  }, [user]);

  const loadChatRooms = async () => {
    setLoading(true);
    try {
      const { data, error } = await getUserChatRooms();
      if (error) {
        console.error('Error loading chat rooms:', error);
      } else {
        setRooms(data || []);
      }
    } catch (error) {
      console.error('Error loading chat rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string | null | undefined) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatLastMessageTime = (timestamp: string | null) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('sr-RS', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('sr-RS', {
        day: 'numeric',
        month: 'short'
      });
    }
  };

  const getOtherParticipant = (room: ChatRoomType) => {
    if (!user) return null;
    const userType = user.user_metadata?.user_type || 'influencer';
    
    if (userType === 'business') {
      return room.influencer_profile;
    } else {
      return room.business_profile;
    }
  };

  const getRoomTypeLabel = (roomType: string) => {
    switch (roomType) {
      case 'campaign_application':
        return 'Kampanja';
      case 'direct_offer':
        return 'Direktna ponuda';
      default:
        return 'Chat';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Poruke
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            Učitavanje...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (rooms.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Poruke
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nemate aktivne razgovore.</p>
            <p className="text-sm">Razgovori će se pojaviti kada prihvatite ponude ili aplikacije.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Poruke ({rooms.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-1">
          {rooms.map((room) => {
            const otherParticipant = getOtherParticipant(room);
            const hasUnread = room.unread_count && room.unread_count > 0;
            
            return (
              <Button
                key={room.id}
                variant="ghost"
                className="w-full justify-start p-4 h-auto hover:bg-muted/50"
                onClick={() => onSelectRoom(room)}
              >
                <div className="flex items-center gap-3 w-full">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={otherParticipant?.avatar_url || ''} />
                    <AvatarFallback>
                      {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 text-left">
                    <div className="flex items-center justify-between">
                      <h4 className={`font-medium ${hasUnread ? 'font-semibold' : ''}`}>
                        {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}
                      </h4>
                      <div className="flex items-center gap-2">
                        {hasUnread && (
                          <Badge variant="destructive" className="text-xs px-2 py-0">
                            {room.unread_count}
                          </Badge>
                        )}
                        <span className="text-xs text-muted-foreground">
                          {formatLastMessageTime(room.last_message_at)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-muted-foreground">
                        @{otherParticipant?.username || 'nepoznato'}
                      </p>
                      <Badge variant="outline" className="text-xs">
                        {getRoomTypeLabel(room.room_type)}
                      </Badge>
                    </div>
                    
                    <p className={`text-sm mt-1 truncate ${hasUnread ? 'font-medium' : 'text-muted-foreground'}`}>
                      {room.room_title}
                    </p>
                  </div>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
