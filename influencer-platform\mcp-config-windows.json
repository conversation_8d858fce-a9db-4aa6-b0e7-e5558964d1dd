{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=awxxrkyommynqlcdwwon", "--access-token=********************************************"]}, "shadcn-ui": {"command": "cmd", "args": ["/c", "npx", "@jpisnice/shadcn-ui-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}}}