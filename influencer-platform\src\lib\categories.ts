import { supabase } from './supabase';
import { Database } from './database.types';

type Category = Database['public']['Tables']['categories']['Row'];

export interface CategoryWithSelection extends Category {
  is_selected?: boolean;
}

/**
 * Get all categories
 */
export async function getCategories() {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching categories:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching categories:', error);
    return { data: null, error };
  }
}

/**
 * Get category by ID
 */
export async function getCategoryById(id: number) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching category:', error);
    return { data: null, error };
  }
}

/**
 * Get category by slug
 */
export async function getCategoryBySlug(slug: string) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching category:', error);
    return { data: null, error };
  }
}

/**
 * Get categories by IDs
 */
export async function getCategoriesByIds(ids: number[]) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .in('id', ids)
      .order('name');

    if (error) {
      console.error('Error fetching categories:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching categories:', error);
    return { data: null, error };
  }
}

/**
 * Search categories by name
 */
export async function searchCategories(query: string) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .ilike('name', `%${query}%`)
      .order('name');

    if (error) {
      console.error('Error searching categories:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error searching categories:', error);
    return { data: null, error };
  }
}

/**
 * Get popular categories (most used by influencers)
 */
export async function getPopularCategories(limit: number = 10) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select(`
        *,
        influencer_categories(count)
      `)
      .order('influencer_categories.count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular categories:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching popular categories:', error);
    return { data: null, error };
  }
}

/**
 * Get categories for a specific influencer
 */
export async function getInfluencerCategories(influencerId: string) {
  try {
    const { data, error } = await supabase
      .from('influencer_categories')
      .select(`
        *,
        categories(*)
      `)
      .eq('influencer_id', influencerId);

    if (error) {
      console.error('Error fetching influencer categories:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching influencer categories:', error);
    return { data: null, error };
  }
}

/**
 * Add categories to influencer
 */
export async function addInfluencerCategories(
  influencerId: string, 
  categoryData: Array<{ category_id: number; is_primary?: boolean }>
) {
  try {
    const { data, error } = await supabase
      .from('influencer_categories')
      .insert(
        categoryData.map(cat => ({
          influencer_id: influencerId,
          category_id: cat.category_id,
          is_primary: cat.is_primary || false
        }))
      );

    if (error) {
      console.error('Error adding influencer categories:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error adding influencer categories:', error);
    return { data: null, error };
  }
}

/**
 * Remove all categories from influencer
 */
export async function removeInfluencerCategories(influencerId: string) {
  try {
    const { error } = await supabase
      .from('influencer_categories')
      .delete()
      .eq('influencer_id', influencerId);

    if (error) {
      console.error('Error removing influencer categories:', error);
      return { error };
    }

    return { error: null };
  } catch (error) {
    console.error('Unexpected error removing influencer categories:', error);
    return { error };
  }
}

/**
 * Update influencer categories (remove old, add new)
 */
export async function updateInfluencerCategories(
  influencerId: string,
  categoryData: Array<{ category_id: number; is_primary?: boolean }>
) {
  try {
    // Remove existing categories
    await removeInfluencerCategories(influencerId);

    // Add new categories
    if (categoryData.length > 0) {
      return await addInfluencerCategories(influencerId, categoryData);
    }

    return { data: null, error: null };
  } catch (error) {
    console.error('Unexpected error updating influencer categories:', error);
    return { data: null, error };
  }
}
