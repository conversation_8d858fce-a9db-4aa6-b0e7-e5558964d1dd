'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getBusiness } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Users, TrendingUp, DollarSign, Plus, FileText, Edit } from 'lucide-react';

export default function BiznisDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [business, setBusiness] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadBusiness();
    }
  }, [user]);

  const loadBusiness = async () => {
    try {
      setLoading(true);
      const { data, error } = await getBusiness(user!.id);

      if (error || !data) {
        // Business profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/biznis');
        return;
      }

      setBusiness(data);
    } catch (err) {
      console.error('Error loading business data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-8">
          {/* Welcome Section */}
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Dobrodošli, {business?.company_name || 'Biznis'}!
            </h1>
            <p className="text-muted-foreground">
              Upravljajte vašim kampanjama i pronađite savršene influencere
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Aktivne kampanje</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Trenutno nema aktivnih kampanja
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Ukupno potrošeno</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0 KM</div>
                <p className="text-xs text-muted-foreground">
                  +0% od prošlog mjeseca
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Saradnje</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Broj završenih saradnji
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Company Profile */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Profil firme</CardTitle>
                <CardDescription>
                  Pregled informacija o vašoj firmi
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dashboard/biznis/profile')}
              >
                <Edit className="mr-2 h-4 w-4" />
                Uredi profil
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Osnovne informacije</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li><strong>Username:</strong> @{business?.profiles?.username}</li>
                    <li><strong>Lokacija:</strong> {business?.profiles?.location || 'Nije navedeno'}</li>
                    <li><strong>Website:</strong> {business?.profiles?.website_url ? (
                      <a href={business.profiles.website_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                        {business.profiles.website_url}
                      </a>
                    ) : 'Nije navedeno'}</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Detalji firme</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li><strong>Industrija:</strong> {business?.industry || 'Nije navedeno'}</li>
                    <li><strong>Veličina:</strong> {business?.company_size || 'Nije navedeno'}</li>
                    <li><strong>Budžet:</strong> {business?.budget_range ? `${business.budget_range} KM` : 'Nije navedeno'}</li>
                  </ul>
                </div>
              </div>
              
              {business?.profiles?.bio && (
                <div>
                  <h4 className="font-medium mb-2">Opis firme</h4>
                  <p className="text-sm text-muted-foreground">{business.profiles.bio}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Brze akcije</CardTitle>
              <CardDescription>
                Šta želite da radite danas?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button
                  className="h-auto p-4 flex flex-col items-start space-y-2"
                  onClick={() => router.push('/campaigns/create')}
                >
                  <div className="flex items-center space-x-2">
                    <Plus className="h-5 w-5" />
                    <span className="font-medium">Kreiraj kampanju</span>
                  </div>
                  <span className="text-sm text-muted-foreground">Kreiraj novu kampanju i pronađi influencere</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start space-y-2"
                  onClick={() => router.push('/marketplace/influencers')}
                >
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span className="font-medium">Pronađi influencere</span>
                  </div>
                  <span className="text-sm text-muted-foreground">Pregledaj i direktno angažuj influencere</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start space-y-2"
                  onClick={() => router.push('/dashboard/campaigns')}
                >
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span className="font-medium">Moje kampanje</span>
                  </div>
                  <span className="text-sm text-muted-foreground">Upravljajte svojim kampanjama i statusima</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start space-y-2"
                  onClick={() => router.push('/dashboard/biznis/applications')}
                >
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span className="font-medium">Pregled aplikacija</span>
                  </div>
                  <span className="text-sm text-muted-foreground">Upravljajte aplikacijama na vaše kampanje</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start space-y-2"
                  onClick={() => router.push('/dashboard/biznis/profile')}
                >
                  <div className="flex items-center space-x-2">
                    <Edit className="h-5 w-5" />
                    <span className="font-medium">Uredi profil</span>
                  </div>
                  <span className="text-sm text-muted-foreground">Ažuriraj informacije o vašoj firmi</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Getting Started */}
          <Card>
            <CardHeader>
              <CardTitle>Početak rada</CardTitle>
              <CardDescription>
                Evo kako možete početi sa InfluConnect platformom
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Kreirajte vašu prvu kampanju</h4>
                    <p className="text-sm text-muted-foreground">Definirajte ciljeve, budžet i tip sadržaja koji želite</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Pregledajte aplikacije</h4>
                    <p className="text-sm text-muted-foreground">Influenceri će aplicirati na vašu kampanju sa prijedlozima</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Izaberite i platite</h4>
                    <p className="text-sm text-muted-foreground">Izaberite najbolje influencere i sigurno platite kroz našu platformu</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }
