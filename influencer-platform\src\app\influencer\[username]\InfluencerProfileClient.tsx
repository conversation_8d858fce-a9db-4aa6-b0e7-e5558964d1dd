'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { MapPin, Users, Star, Verified, Mail, MessageCircle, Calendar, Globe } from 'lucide-react';
import { DirectOfferForm } from '@/components/offers/DirectOfferForm';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import Link from 'next/link';
import type { PublicInfluencerProfile } from '@/lib/marketplace';

interface InfluencerProfileClientProps {
  profile: PublicInfluencerProfile;
}

export function InfluencerProfileClient({ profile }: InfluencerProfileClientProps) {
  const { user } = useAuth();
  const [showOfferForm, setShowOfferForm] = useState(false);

  const getInitials = (name: string | null) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'male': return 'Muški';
      case 'female': return 'Ženski';
      case 'other': return 'Ostalo';
      default: return '';
    }
  };

  const handleOfferSuccess = () => {
    setShowOfferForm(false);
    toast.success('Ponuda je uspješno poslana!');
  };

  const handleSendOffer = () => {
    if (!user) {
      toast.error('Morate biti prijavljeni da biste poslali ponudu');
      return;
    }
    setShowOfferForm(true);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb */}
      <div className="border-b bg-muted/30">
        <div className="container mx-auto px-4 py-3">
          <nav className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link href="/marketplace/influencers" className="hover:text-foreground transition-colors">
              Marketplace
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">@{profile.username}</span>
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Avatar i osnovne info */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <Avatar className="h-24 w-24 lg:h-32 lg:w-32">
                <AvatarImage src={profile.avatar_url || ''} alt={profile.full_name} />
                <AvatarFallback className="text-lg lg:text-xl">
                  {getInitials(profile.full_name)}
                </AvatarFallback>
              </Avatar>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl lg:text-3xl font-bold">{profile.full_name || profile.username}</h1>
                  {profile.is_verified && (
                    <Verified className="h-6 w-6 text-blue-500 fill-current" />
                  )}
                </div>
                <p className="text-lg text-muted-foreground">@{profile.username}</p>
                
                {/* Lokacija i demografija */}
                <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                  {profile.location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {profile.location}
                    </div>
                  )}
                  {profile.age && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {profile.age} godina
                    </div>
                  )}
                  {profile.gender && (
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {getGenderLabel(profile.gender)}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* CTA dugmad */}
            <div className="lg:ml-auto flex flex-col sm:flex-row gap-3">
              <Dialog open={showOfferForm} onOpenChange={setShowOfferForm}>
                <DialogTrigger asChild>
                  <Button size="lg" className="flex items-center gap-2" onClick={handleSendOffer}>
                    <MessageCircle className="h-5 w-5" />
                    Pošaljite ponudu
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Direktna ponuda</DialogTitle>
                  </DialogHeader>
                  <DirectOfferForm
                    influencerId={profile.id}
                    influencerName={profile.full_name}
                    onSuccess={handleOfferSuccess}
                    onCancel={() => setShowOfferForm(false)}
                  />
                </DialogContent>
              </Dialog>
              <Button variant="outline" size="lg" className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Kontakt
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Leva kolona - Bio i kategorije */}
          <div className="lg:col-span-2 space-y-6">
            {/* Bio */}
            {profile.bio && (
              <Card>
                <CardHeader>
                  <CardTitle>O meni</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">{profile.bio}</p>
                </CardContent>
              </Card>
            )}

            {/* Kategorije */}
            {profile.categories && profile.categories.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Kategorije</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {profile.categories.map((category, index) => (
                      <Badge key={index} variant="secondary" className="text-sm">
                        {category.category_name}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Platforme */}
            {profile.platforms && profile.platforms.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Društvene mreže</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {profile.platforms.map((platform, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">{platform.platform_icon}</span>
                          <div>
                            <p className="font-medium">{platform.platform_name}</p>
                            {platform.handle && (
                              <p className="text-sm text-muted-foreground">@{platform.handle}</p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">{formatFollowers(platform.followers_count)}</p>
                          <p className="text-xs text-muted-foreground">pratilaca</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Portfolio */}
            {profile.portfolio_urls && profile.portfolio_urls.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Portfolio
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {profile.portfolio_urls.map((url, index) => (
                      <a
                        key={index}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-blue-600 hover:underline">{url}</span>
                        </div>
                      </a>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Desna kolona - Cijene */}
          <div className="space-y-6">
            {/* Pricing */}
            {profile.pricing && profile.pricing.length > 0 && (
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5" />
                    Cijene usluga
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {profile.pricing.map((price, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{price.platform_icon}</span>
                          <span className="font-medium text-sm">{price.content_type_name}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">{price.platform_name}</p>
                      </div>
                      <div className="text-right">
                        <span className="font-bold text-lg">{price.price} {price.currency}</span>
                      </div>
                    </div>
                  ))}
                  
                  {/* Ukupan range */}
                  <div className="pt-4 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Cijena od:</span>
                      <span className="font-bold">{profile.min_price} KM</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Cijena do:</span>
                      <span className="font-bold">{profile.max_price} KM</span>
                    </div>
                  </div>

                  {/* CTA dugme */}
                  <Button className="w-full mt-4" size="lg" onClick={handleSendOffer}>
                    <MessageCircle className="h-5 w-5 mr-2" />
                    Pošaljite ponudu
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Statistike */}
            <Card>
              <CardHeader>
                <CardTitle>Statistike</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Ukupno pratilaca:</span>
                  <span className="font-medium">{formatFollowers(profile.total_followers)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Broj platformi:</span>
                  <span className="font-medium">{profile.platforms?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Kategorije:</span>
                  <span className="font-medium">{profile.categories?.length || 0}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
