'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Send, ArrowLeft } from 'lucide-react';
import { ChatMessage, ChatRoom as ChatRoomType } from '@/lib/chat';
import { getChatMessages, sendChatMessage, markMessagesAsRead } from '@/lib/chat';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { ChatContextBar } from './ChatContextBar';

interface ChatRoomProps {
  room: ChatRoomType;
  onBack: () => void;
}

export function ChatRoom({ room, onBack }: ChatRoomProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    loadMessages();
    
    // Subscribe to new messages
    const channel = supabase
      .channel(`chat_room_${room.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${room.id}`,
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          setMessages(prev => [...prev, newMessage]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [room.id]);

  const loadMessages = async () => {
    setLoading(true);
    try {
      const { data, error } = await getChatMessages(room.id);
      if (error) {
        console.error('Error loading messages:', error);
      } else {
        setMessages(data || []);
        // Mark unread messages as read (messages not sent by current user)
        if (user && data) {
          const unreadMessageIds = data
            .filter(msg => msg.sender_id !== user.id && !msg.read_at)
            .map(msg => msg.id);

          if (unreadMessageIds.length > 0) {
            await markMessagesAsRead(room.id, unreadMessageIds);
          }
        }
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !user || sending) return;

    setSending(true);
    try {
      const userType = user.user_metadata?.user_type || 'influencer';
      const { data, error } = await sendChatMessage(
        room.id,
        newMessage.trim()
      );

      if (error) {
        console.error('Error sending message:', error);
      } else {
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const getInitials = (name: string | null | undefined) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('sr-RS', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('sr-RS', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const isMyMessage = (message: ChatMessage) => {
    return user && message.sender_id === user.id;
  };

  const getOtherParticipant = () => {
    if (!user) return null;
    const userType = user.user_metadata?.user_type || 'influencer';
    
    if (userType === 'business') {
      return room.influencer_profile;
    } else {
      return room.business_profile;
    }
  };

  const otherParticipant = getOtherParticipant();

  return (
    <div className="h-full flex flex-col">
      {/* Context Bar */}
      <ChatContextBar
        campaignApplicationId={room.campaign_application_id}
        offerId={room.offer_id}
      />

      <Card className="flex-1 flex flex-col">
        <CardHeader className="flex-shrink-0 border-b">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="p-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <Avatar className="h-10 w-10">
              <AvatarImage src={otherParticipant?.avatar_url || ''} />
              <AvatarFallback>
                {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <CardTitle className="text-lg">
                {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                @{otherParticipant?.username || 'nepoznato'}
              </p>
            </div>

            <Badge variant="secondary">
              {room.room_type === 'campaign_application' ? 'Kampanja' : 'Direktna ponuda'}
            </Badge>
          </div>
        </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {loading ? (
            <div className="text-center text-muted-foreground">
              Učitavanje poruka...
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-muted-foreground">
              Nema poruka. Pošaljite prvu poruku!
            </div>
          ) : (
            messages.map((message, index) => {
              const showDate = index === 0 || 
                formatDate(message.created_at || '') !== formatDate(messages[index - 1]?.created_at || '');
              
              return (
                <div key={message.id}>
                  {showDate && (
                    <div className="text-center text-xs text-muted-foreground mb-4">
                      {formatDate(message.created_at || '')}
                    </div>
                  )}
                  
                  <div className={`flex ${isMyMessage(message) ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[70%] ${isMyMessage(message) ? 'order-2' : 'order-1'}`}>
                      <div
                        className={`rounded-lg px-3 py-2 ${
                          isMyMessage(message)
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        <p className="text-sm">{message.message_text}</p>
                      </div>
                      <p className={`text-xs text-muted-foreground mt-1 ${
                        isMyMessage(message) ? 'text-right' : 'text-left'
                      }`}>
                        {formatTime(message.created_at || '')}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="border-t p-4">
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Napišite poruku..."
              disabled={sending}
              className="flex-1"
            />
            <Button type="submit" disabled={!newMessage.trim() || sending}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
