-- Kreiranje categories tabele sa bosanskim kategorijama
-- Pokrenuti u Supabase SQL Editor

-- Kreiranje categories tabele
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  icon VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Umetanje predefinisanih kategorija (na bosanskom)
INSERT INTO categories (name, slug, description, icon) VALUES
('Moda', 'moda', '<PERSON>d<PERSON><PERSON><PERSON>, modni trendovi, styling i modni savjeti', '👗'),
('Ljepota', 'ljepota', '<PERSON><PERSON><PERSON><PERSON>, š<PERSON>ka, njega kože i beauty savjeti', '💄'),
('Putovanja', 'putovanja', '<PERSON><PERSON><PERSON><PERSON>, putni savjeti, kulture i avanture', '✈️'),
('Zdravlje i fitness', 'zdravlje-fitness', '<PERSON><PERSON><PERSON><PERSON><PERSON>, zdrava ishrana, wellness i mentalno zdravlje', '💪'),
('<PERSON>rana i piće', 'hrana-pice', 'Recepti, restorani, kulinarstvo i gastronomija', '🍽️'),
('Komedija i zabava', 'komedija-zabava', 'Humor, stand-up, zabavni sadržaj i viral videi', '😂'),
('Umjetnost i fotografija', 'umjetnost-fotografija', 'Vizuelna umjetnost, fotografija, dizajn i kreativnost', '🎨'),
('Muzika i ples', 'muzika-ples', 'Muzički sadržaj, plesne koreografije i performanse', '🎵'),
('Porodica i djeca', 'porodica-djeca', 'Roditeljstvo, dječiji sadržaj, porodične aktivnosti', '👨‍👩‍👧‍👦'),
('Preduzetništvo i biznis', 'preduzetnistvo-biznis', 'Poslovni savjeti, startup kultura, motivacija', '💼'),
('Životinje i kućni ljubimci', 'zivotinje-ljubimci', 'Pet care, životinjski sadržaj, veterinarski savjeti', '🐕'),
('Edukacija', 'edukacija', 'Obrazovni sadržaj, tutorijali, naučne teme', '📚'),
('Avantura i priroda', 'avantura-priroda', 'Outdoor aktivnosti, planinarenje, ekstremni sportovi', '🏔️'),
('Sport i atletika', 'sport-atletika', 'Sportski sadržaj, treninzi, sportske vesti', '⚽'),
('Tehnologija', 'tehnologija', 'Tech reviews, gadgets, programiranje, inovacije', '💻'),
('Gaming', 'gaming', 'Video igre, gaming reviews, esports, streaming', '🎮'),
('Zdravstvo', 'zdravstvo', 'Medicinski savjeti, zdravstvena edukacija, wellness', '🏥'),
('Automobilizam', 'automobilizam', 'Automobili, motori, auto reviews, tuning', '🚗'),
('Zanatstvo', 'zanatstvo', 'DIY projekti, ručni rad, tradicionalni zanati', '🔨');

-- Kreiranje junction tabele za influencer kategorije
CREATE TABLE IF NOT EXISTS influencer_categories (
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (influencer_id, category_id)
);

-- Kreiranje junction tabele za biznis target kategorije
CREATE TABLE IF NOT EXISTS business_target_categories (
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (business_id, category_id)
);

-- Kreiranje indeksa za performanse
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);
CREATE INDEX IF NOT EXISTS idx_influencer_categories_influencer ON influencer_categories(influencer_id);
CREATE INDEX IF NOT EXISTS idx_influencer_categories_category ON influencer_categories(category_id);
CREATE INDEX IF NOT EXISTS idx_influencer_categories_primary ON influencer_categories(is_primary);
CREATE INDEX IF NOT EXISTS idx_business_target_categories_business ON business_target_categories(business_id);
CREATE INDEX IF NOT EXISTS idx_business_target_categories_category ON business_target_categories(category_id);

-- RLS (Row Level Security) policies
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE influencer_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_target_categories ENABLE ROW LEVEL SECURITY;

-- RLS policies za categories (svi mogu čitati)
CREATE POLICY "Anyone can view categories" ON categories FOR SELECT USING (true);

-- RLS policies za influencer_categories
CREATE POLICY "Anyone can view influencer categories" ON influencer_categories FOR SELECT USING (true);
CREATE POLICY "Influencers can manage own categories" ON influencer_categories 
  FOR ALL USING (auth.uid() = influencer_id);

-- RLS policies za business_target_categories
CREATE POLICY "Anyone can view business target categories" ON business_target_categories FOR SELECT USING (true);
CREATE POLICY "Businesses can manage own target categories" ON business_target_categories 
  FOR ALL USING (auth.uid() = business_id);

-- Funkcija za validaciju broja kategorija (maksimalno 3)
CREATE OR REPLACE FUNCTION validate_max_categories()
RETURNS TRIGGER AS $$
BEGIN
  -- Provjeri za influencer kategorije
  IF TG_TABLE_NAME = 'influencer_categories' THEN
    IF (SELECT COUNT(*) FROM influencer_categories WHERE influencer_id = NEW.influencer_id) >= 3 THEN
      RAISE EXCEPTION 'Influencer može imati maksimalno 3 kategorije';
    END IF;
  END IF;
  
  -- Provjeri za biznis target kategorije
  IF TG_TABLE_NAME = 'business_target_categories' THEN
    IF (SELECT COUNT(*) FROM business_target_categories WHERE business_id = NEW.business_id) >= 3 THEN
      RAISE EXCEPTION 'Biznis može imati maksimalno 3 target kategorije';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggeri za validaciju
CREATE TRIGGER validate_influencer_categories_count
  BEFORE INSERT ON influencer_categories
  FOR EACH ROW EXECUTE FUNCTION validate_max_categories();

CREATE TRIGGER validate_business_categories_count
  BEFORE INSERT ON business_target_categories
  FOR EACH ROW EXECUTE FUNCTION validate_max_categories();

-- Funkcija za dobijanje kategorija sa brojem influencera
CREATE OR REPLACE FUNCTION get_categories_with_counts()
RETURNS TABLE (
  id INTEGER,
  name VARCHAR(100),
  slug VARCHAR(100),
  description TEXT,
  icon VARCHAR(50),
  influencer_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.name,
    c.slug,
    c.description,
    c.icon,
    COUNT(ic.influencer_id) as influencer_count
  FROM categories c
  LEFT JOIN influencer_categories ic ON c.id = ic.category_id
  GROUP BY c.id, c.name, c.slug, c.description, c.icon
  ORDER BY c.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funkcija za pretragu influencera po kategorijama
CREATE OR REPLACE FUNCTION search_influencers_by_categories(category_ids INTEGER[])
RETURNS TABLE (
  influencer_id UUID,
  category_matches INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ic.influencer_id,
    COUNT(ic.category_id)::INTEGER as category_matches
  FROM influencer_categories ic
  WHERE ic.category_id = ANY(category_ids)
  GROUP BY ic.influencer_id
  ORDER BY category_matches DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
