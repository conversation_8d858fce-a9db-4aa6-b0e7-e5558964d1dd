-- FAZA 3A: Marketplace schema za pretragu i javne profile
-- Pokrenuti u Supabase SQL Editor

-- 1. <PERSON>reiranje materialized view za optimizovanu pretragu influencera
CREATE MATERIALIZED VIEW IF NOT EXISTS influencer_search_view AS
SELECT 
  i.id,
  p.username,
  p.full_name,
  p.avatar_url,
  p.bio,
  p.location,
  i.gender,
  i.age,
  i.is_verified,
  
  -- Kategorije kao array
  COALESCE(
    ARRAY_AGG(DISTINCT c.name) FILTER (WHERE c.id IS NOT NULL),
    ARRAY[]::VARCHAR[]
  ) as categories,
  
  COALESCE(
    ARRAY_AGG(DISTINCT c.id) FILTER (WHERE c.id IS NOT NULL),
    ARRAY[]::INTEGER[]
  ) as category_ids,
  
  -- <PERSON><PERSON> kao JSON
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', plat.id,
        'platform_name', plat.name,
        'platform_icon', plat.icon,
        'handle', ip.handle,
        'followers_count', ip.followers_count,
        'is_verified', ip.is_verified
      ) ORDER BY plat.name
    ) FILTER (WHERE plat.id IS NOT NULL),
    '[]'::json
  ) as platforms,

  -- Cijene kao JSON
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', pricing_plat.id,
        'platform_name', pricing_plat.name,
        'content_type_id', ct.id,
        'content_type_name', ct.name,
        'price', ipp.price,
        'currency', ipp.currency
      ) ORDER BY pricing_plat.name, ct.name
    ) FILTER (WHERE ipp.id IS NOT NULL AND ipp.is_available = true),
    '[]'::json
  ) as pricing,
  
  -- Minimum i maximum cijena za sortiranje
  COALESCE(MIN(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as min_price,
  COALESCE(MAX(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as max_price,
  
  -- Ukupan broj pratilaca
  COALESCE(SUM(ip.followers_count), 0) as total_followers,
  
  -- Full-text search vector
  to_tsvector('simple', 
    COALESCE(p.full_name, '') || ' ' ||
    COALESCE(p.username, '') || ' ' ||
    COALESCE(p.bio, '') || ' ' ||
    COALESCE(p.location, '') || ' ' ||
    COALESCE(STRING_AGG(DISTINCT c.name, ' '), '')
  ) as search_vector,
  
  p.created_at,
  p.updated_at

FROM influencers i
JOIN profiles p ON i.id = p.id
LEFT JOIN influencer_categories ic ON i.id = ic.influencer_id
LEFT JOIN categories c ON ic.category_id = c.id
LEFT JOIN influencer_platforms ip ON i.id = ip.influencer_id AND ip.is_active = true
LEFT JOIN platforms plat ON ip.platform_id = plat.id
LEFT JOIN influencer_platform_pricing ipp ON i.id = ipp.influencer_id AND ipp.is_available = true
LEFT JOIN platforms pricing_plat ON ipp.platform_id = pricing_plat.id
LEFT JOIN content_types ct ON ipp.content_type_id = ct.id

WHERE p.user_type = 'influencer'
GROUP BY i.id, p.username, p.full_name, p.avatar_url, p.bio, p.location, 
         i.gender, i.age, i.is_verified, p.created_at, p.updated_at;

-- 2. Kreiranje indeksa za performanse
-- Unique index potreban za CONCURRENTLY refresh
CREATE UNIQUE INDEX IF NOT EXISTS idx_influencer_search_unique_id ON influencer_search_view(id);

-- Ostali indeksi za performanse
CREATE INDEX IF NOT EXISTS idx_influencer_search_categories ON influencer_search_view USING GIN(category_ids);
CREATE INDEX IF NOT EXISTS idx_influencer_search_location ON influencer_search_view(location);
CREATE INDEX IF NOT EXISTS idx_influencer_search_gender ON influencer_search_view(gender);
CREATE INDEX IF NOT EXISTS idx_influencer_search_age ON influencer_search_view(age);
CREATE INDEX IF NOT EXISTS idx_influencer_search_verified ON influencer_search_view(is_verified);
CREATE INDEX IF NOT EXISTS idx_influencer_search_min_price ON influencer_search_view(min_price);
CREATE INDEX IF NOT EXISTS idx_influencer_search_max_price ON influencer_search_view(max_price);
CREATE INDEX IF NOT EXISTS idx_influencer_search_followers ON influencer_search_view(total_followers);
CREATE INDEX IF NOT EXISTS idx_influencer_search_vector ON influencer_search_view USING GIN(search_vector);

-- 3. Funkcija za refresh materialized view
CREATE OR REPLACE FUNCTION refresh_influencer_search_view()
RETURNS VOID AS $$
BEGIN
  -- Pokušaj concurrent refresh, ako ne uspe koristi obični refresh
  BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY influencer_search_view;
  EXCEPTION WHEN OTHERS THEN
    -- Fallback na obični refresh ako concurrent ne radi
    REFRESH MATERIALIZED VIEW influencer_search_view;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Trigger za automatsko refresh-ovanje view-a
CREATE OR REPLACE FUNCTION trigger_refresh_influencer_search()
RETURNS TRIGGER AS $$
BEGIN
  -- Refresh view u background-u (async)
  PERFORM pg_notify('refresh_search_view', '');
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Kreiranje trigger-a na relevantnim tabelama
DROP TRIGGER IF EXISTS refresh_search_on_profile_change ON profiles;
CREATE TRIGGER refresh_search_on_profile_change
  AFTER INSERT OR UPDATE OR DELETE ON profiles
  FOR EACH ROW EXECUTE FUNCTION trigger_refresh_influencer_search();

DROP TRIGGER IF EXISTS refresh_search_on_influencer_change ON influencers;
CREATE TRIGGER refresh_search_on_influencer_change
  AFTER INSERT OR UPDATE OR DELETE ON influencers
  FOR EACH ROW EXECUTE FUNCTION trigger_refresh_influencer_search();

DROP TRIGGER IF EXISTS refresh_search_on_category_change ON influencer_categories;
CREATE TRIGGER refresh_search_on_category_change
  AFTER INSERT OR UPDATE OR DELETE ON influencer_categories
  FOR EACH ROW EXECUTE FUNCTION trigger_refresh_influencer_search();

DROP TRIGGER IF EXISTS refresh_search_on_platform_change ON influencer_platforms;
CREATE TRIGGER refresh_search_on_platform_change
  AFTER INSERT OR UPDATE OR DELETE ON influencer_platforms
  FOR EACH ROW EXECUTE FUNCTION trigger_refresh_influencer_search();

DROP TRIGGER IF EXISTS refresh_search_on_pricing_change ON influencer_platform_pricing;
CREATE TRIGGER refresh_search_on_pricing_change
  AFTER INSERT OR UPDATE OR DELETE ON influencer_platform_pricing
  FOR EACH ROW EXECUTE FUNCTION trigger_refresh_influencer_search();

-- 5. RLS policy za search view
ALTER MATERIALIZED VIEW influencer_search_view OWNER TO postgres;
-- Note: Materialized views ne podržavaju RLS, ali su read-only i bezbedni

-- 6. Funkcija za pretragu influencera sa filterima
CREATE OR REPLACE FUNCTION search_influencers(
  search_query TEXT DEFAULT '',
  category_ids INTEGER[] DEFAULT NULL,
  platform_ids INTEGER[] DEFAULT NULL,
  content_type_ids INTEGER[] DEFAULT NULL,
  min_price_filter DECIMAL DEFAULT NULL,
  max_price_filter DECIMAL DEFAULT NULL,
  min_followers INTEGER DEFAULT NULL,
  max_followers INTEGER DEFAULT NULL,
  location_filter TEXT DEFAULT NULL,
  gender_filter TEXT DEFAULT NULL,
  min_age INTEGER DEFAULT NULL,
  max_age INTEGER DEFAULT NULL,
  verified_only BOOLEAN DEFAULT FALSE,
  sort_by TEXT DEFAULT 'relevance', -- 'relevance', 'price_asc', 'price_desc', 'followers_desc', 'newest'
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  username VARCHAR,
  full_name VARCHAR,
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR,
  gender VARCHAR,
  age INTEGER,
  is_verified BOOLEAN,
  categories VARCHAR[],
  platforms JSON,
  pricing JSON,
  min_price DECIMAL,
  max_price DECIMAL,
  total_followers BIGINT,
  relevance_score REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    isv.id,
    isv.username,
    isv.full_name,
    isv.avatar_url,
    isv.bio,
    isv.location,
    isv.gender,
    isv.age,
    isv.is_verified,
    isv.categories,
    isv.platforms,
    isv.pricing,
    isv.min_price,
    isv.max_price,
    isv.total_followers,
    CASE 
      WHEN search_query = '' THEN 1.0
      ELSE ts_rank(isv.search_vector, plainto_tsquery('simple', search_query))
    END as relevance_score
  FROM influencer_search_view isv
  WHERE 
    -- Text search
    (search_query = '' OR isv.search_vector @@ plainto_tsquery('simple', search_query))
    
    -- Category filter
    AND (category_ids IS NULL OR isv.category_ids && category_ids)
    
    -- Platform filter (check if any of user's platforms match)
    AND (platform_ids IS NULL OR EXISTS (
      SELECT 1 FROM json_array_elements(isv.platforms) as p
      WHERE (p->>'platform_id')::INTEGER = ANY(platform_ids)
    ))
    
    -- Content type filter (check if user has pricing for any of these content types)
    AND (content_type_ids IS NULL OR EXISTS (
      SELECT 1 FROM json_array_elements(isv.pricing) as pr
      WHERE (pr->>'content_type_id')::INTEGER = ANY(content_type_ids)
    ))
    
    -- Price filters
    AND (min_price_filter IS NULL OR isv.max_price >= min_price_filter)
    AND (max_price_filter IS NULL OR isv.min_price <= max_price_filter)
    
    -- Followers filters
    AND (min_followers IS NULL OR isv.total_followers >= min_followers)
    AND (max_followers IS NULL OR isv.total_followers <= max_followers)
    
    -- Location filter
    AND (location_filter IS NULL OR isv.location ILIKE '%' || location_filter || '%')
    
    -- Gender filter
    AND (gender_filter IS NULL OR isv.gender = gender_filter)
    
    -- Age filters
    AND (min_age IS NULL OR isv.age >= min_age)
    AND (max_age IS NULL OR isv.age <= max_age)
    
    -- Verified filter
    AND (NOT verified_only OR isv.is_verified = true)
    
  ORDER BY
    CASE 
      WHEN sort_by = 'relevance' THEN 
        CASE 
          WHEN search_query = '' THEN 1.0
          ELSE ts_rank(isv.search_vector, plainto_tsquery('simple', search_query))
        END
    END DESC,
    CASE WHEN sort_by = 'price_asc' THEN isv.min_price END ASC,
    CASE WHEN sort_by = 'price_desc' THEN isv.max_price END DESC,
    CASE WHEN sort_by = 'followers_desc' THEN isv.total_followers END DESC,
    CASE WHEN sort_by = 'newest' THEN isv.created_at END DESC,
    isv.created_at DESC
    
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Funkcija za dobijanje javnog profila influencera
CREATE OR REPLACE FUNCTION get_public_influencer_profile(influencer_username TEXT)
RETURNS TABLE (
  id UUID,
  username VARCHAR,
  full_name VARCHAR,
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR,
  gender VARCHAR,
  age INTEGER,
  is_verified BOOLEAN,
  categories JSON,
  platforms JSON,
  pricing JSON,
  portfolio_urls TEXT[],
  total_followers BIGINT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    isv.id,
    isv.username,
    isv.full_name,
    isv.avatar_url,
    isv.bio,
    isv.location,
    isv.gender,
    isv.age,
    isv.is_verified,
    
    -- Kategorije kao JSON sa ikonama
    COALESCE(
      (SELECT JSON_AGG(
        JSON_BUILD_OBJECT(
          'id', c.id,
          'name', c.name,
          'icon', c.icon,
          'is_primary', ic.is_primary
        ) ORDER BY ic.is_primary DESC, c.name
      )
      FROM influencer_categories ic
      JOIN categories c ON ic.category_id = c.id
      WHERE ic.influencer_id = isv.id),
      '[]'::json
    ) as categories,
    
    isv.platforms,
    isv.pricing,
    
    -- Portfolio URLs iz influencers tabele
    (SELECT i.portfolio_urls FROM influencers i WHERE i.id = isv.id),
    
    isv.total_followers,
    isv.created_at
    
  FROM influencer_search_view isv
  WHERE isv.username = influencer_username
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Inicijalno kreiranje materialized view
SELECT refresh_influencer_search_view();
