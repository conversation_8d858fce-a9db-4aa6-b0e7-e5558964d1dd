'use client';

import { useSearchParams } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Chat } from '@/components/chat/Chat';

export default function ChatPage() {
  const searchParams = useSearchParams();
  const roomId = searchParams.get('room');
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Poruke</h1>
          <p className="text-muted-foreground">
            Komunicirajte sa partnerima o kampanjama i ponudama
          </p>
        </div>
        
        <Chat initialRoomId={roomId || undefined} />
      </div>
    </DashboardLayout>
  );
}
