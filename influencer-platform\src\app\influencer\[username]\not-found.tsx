import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, ArrowLeft, Users } from 'lucide-react';

export default function InfluencerNotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-8 text-center">
          <div className="mb-6">
            <Users className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h1 className="text-2xl font-bold mb-2">Influencer nije pronađen</h1>
            <p className="text-muted-foreground">
              Traženi influencer profil ne postoji ili je uklonjen.
            </p>
          </div>
          
          <div className="space-y-3">
            <Link href="/marketplace/influencers">
              <Button className="w-full">
                <Search className="h-4 w-4 mr-2" />
                Pretražite influencere
              </Button>
            </Link>
            
            <Link href="/dashboard">
              <Button variant="outline" className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Nazad na dashboard
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
