'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { ChatPermissionCard } from '@/components/chat/ChatPermissionCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, MessageCircle } from 'lucide-react';
import { ChatPermission, getUserChatPermissions } from '@/lib/chat-permissions';

export default function ChatPermissionsPage() {
  const [permissions, setPermissions] = useState<ChatPermission[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPermissions();
  }, []);

  const loadPermissions = async () => {
    setLoading(true);
    try {
      const data = await getUserChatPermissions();
      setPermissions(data);
    } catch (error) {
      console.error('Error loading chat permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const pendingPermissions = permissions.filter(p => !p.chat_enabled);
  const enabledPermissions = permissions.filter(p => p.chat_enabled);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            Chat dozvole
          </h1>
          <p className="text-muted-foreground">
            Upravljajte dozvolama za komunikaciju sa partnerima
          </p>
        </div>

        {loading ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center text-muted-foreground">
                Učitavanje dozvola...
              </div>
            </CardContent>
          </Card>
        ) : permissions.length === 0 ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center text-muted-foreground">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nemate chat dozvola.</p>
                <p className="text-sm">Dozvole će se pojaviti kada prihvatite ponude ili aplikacije.</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Pending Permissions */}
            {pendingPermissions.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">
                  Čeka odobrenje ({pendingPermissions.length})
                </h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {pendingPermissions.map((permission) => (
                    <ChatPermissionCard
                      key={permission.id}
                      permission={permission}
                      onUpdate={loadPermissions}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Enabled Permissions */}
            {enabledPermissions.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">
                  Omogućeni chat-ovi ({enabledPermissions.length})
                </h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {enabledPermissions.map((permission) => (
                    <ChatPermissionCard
                      key={permission.id}
                      permission={permission}
                      onUpdate={loadPermissions}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
