'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext';
import { getProfile, updateProfile, getInfluencer, updateInfluencer } from '@/lib/profiles';
import { Loader2, Save, User, Camera, Globe, DollarSign } from 'lucide-react';
import { toast } from 'sonner';

const profileSchema = z.object({
  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),
  bio: z.string().max(500, 'Bio može imati maksimalno 500 karaktera').optional(),
  location: z.string().optional(),
  website_url: z.string().url('Neispravna URL adresa').optional().or(z.literal('')),
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
  price_per_post: z.number().min(0).optional(),
  price_per_story: z.number().min(0).optional(),
  price_per_reel: z.number().min(0).optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

export default function InfluencerProfilePage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [influencer, setInfluencer] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load profile
      const { data: profileData, error: profileError } = await getProfile(user!.id);
      if (profileError || !profileData) {
        toast.error('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load influencer data
      const { data: influencerData, error: influencerError } = await getInfluencer(user!.id);
      if (influencerError || !influencerData) {
        toast.error('Greška pri učitavanju influencer podataka');
        return;
      }
      setInfluencer(influencerData);
      
      // Popuni formu sa postojećim podacima
      reset({
        username: profileData.username || '',
        bio: profileData.bio || '',
        location: profileData.location || '',
        website_url: profileData.website_url || '',
        instagram_handle: influencerData.instagram_handle || '',
        instagram_followers: influencerData.instagram_followers || 0,
        tiktok_handle: influencerData.tiktok_handle || '',
        tiktok_followers: influencerData.tiktok_followers || 0,
        youtube_handle: influencerData.youtube_handle || '',
        youtube_subscribers: influencerData.youtube_subscribers || 0,
        price_per_post: influencerData.price_per_post || 0,
        price_per_story: influencerData.price_per_story || 0,
        price_per_reel: influencerData.price_per_reel || 0,
      });
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProfileForm) => {
    if (!user) return;

    setSaving(true);
    try {
      // Update profile
      const { error: profileError } = await updateProfile(user.id, {
        username: data.username,
        bio: data.bio || null,
        location: data.location || null,
        website_url: data.website_url || null,
      });

      if (profileError) {
        if (profileError.message.includes('username')) {
          toast.error('Username je već zauzet');
        } else {
          toast.error('Greška pri ažuriranju profila');
        }
        return;
      }

      // Update influencer data
      const { error: influencerError } = await updateInfluencer(user.id, {
        instagram_handle: data.instagram_handle || null,
        instagram_followers: data.instagram_followers || 0,
        tiktok_handle: data.tiktok_handle || null,
        tiktok_followers: data.tiktok_followers || 0,
        youtube_handle: data.youtube_handle || null,
        youtube_subscribers: data.youtube_subscribers || 0,
        price_per_post: data.price_per_post || null,
        price_per_story: data.price_per_story || null,
        price_per_reel: data.price_per_reel || null,
      });

      if (influencerError) {
        toast.error('Greška pri ažuriranju influencer podataka');
        return;
      }

      toast.success('Profil je uspješno ažuriran');
      loadData(); // Refresh data
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Postavke profila</h1>
          <p className="text-muted-foreground mt-2">
            Upravljajte svojim javnim profilom koji vide brendovi i korisnici
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Osnovne informacije */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <CardTitle>Osnovne informacije</CardTitle>
              </div>
              <CardDescription>
                Ovi podaci se prikazuju na vašem javnom profilu
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="username">Username *</Label>
                  <Input
                    id="username"
                    {...register('username')}
                    placeholder="@vasusername"
                  />
                  {errors.username && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.username.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="location">Lokacija</Label>
                  <Input
                    id="location"
                    {...register('location')}
                    placeholder="Sarajevo, BiH"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  {...register('bio')}
                  placeholder="Opišite sebe u nekoliko rečenica..."
                  rows={4}
                />
                {errors.bio && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.bio.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="website_url">Website</Label>
                <Input
                  id="website_url"
                  {...register('website_url')}
                  placeholder="https://vaswebsite.com"
                />
                {errors.website_url && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.website_url.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Društvene mreže */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <CardTitle>Društvene mreže</CardTitle>
              </div>
              <CardDescription>
                Dodajte svoje profile na društvenim mrežama
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Instagram */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="instagram_handle">Instagram handle</Label>
                  <Input
                    id="instagram_handle"
                    {...register('instagram_handle')}
                    placeholder="@vasinstagram"
                  />
                </div>
                <div>
                  <Label htmlFor="instagram_followers">Instagram pratilaca</Label>
                  <Input
                    id="instagram_followers"
                    type="number"
                    {...register('instagram_followers', { valueAsNumber: true })}
                    placeholder="1000"
                  />
                </div>
              </div>

              {/* TikTok */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="tiktok_handle">TikTok handle</Label>
                  <Input
                    id="tiktok_handle"
                    {...register('tiktok_handle')}
                    placeholder="@vastiktok"
                  />
                </div>
                <div>
                  <Label htmlFor="tiktok_followers">TikTok pratilaca</Label>
                  <Input
                    id="tiktok_followers"
                    type="number"
                    {...register('tiktok_followers', { valueAsNumber: true })}
                    placeholder="500"
                  />
                </div>
              </div>

              {/* YouTube */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="youtube_handle">YouTube kanal</Label>
                  <Input
                    id="youtube_handle"
                    {...register('youtube_handle')}
                    placeholder="@vaskanal"
                  />
                </div>
                <div>
                  <Label htmlFor="youtube_subscribers">YouTube pretplatnika</Label>
                  <Input
                    id="youtube_subscribers"
                    type="number"
                    {...register('youtube_subscribers', { valueAsNumber: true })}
                    placeholder="200"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cijene */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <CardTitle>Cijene</CardTitle>
              </div>
              <CardDescription>
                Postavite svoje cijene za različite tipove sadržaja (u KM)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="price_per_post">Cijena po postu</Label>
                  <Input
                    id="price_per_post"
                    type="number"
                    {...register('price_per_post', { valueAsNumber: true })}
                    placeholder="100"
                  />
                </div>
                <div>
                  <Label htmlFor="price_per_story">Cijena po story</Label>
                  <Input
                    id="price_per_story"
                    type="number"
                    {...register('price_per_story', { valueAsNumber: true })}
                    placeholder="50"
                  />
                </div>
                <div>
                  <Label htmlFor="price_per_reel">Cijena po reel</Label>
                  <Input
                    id="price_per_reel"
                    type="number"
                    {...register('price_per_reel', { valueAsNumber: true })}
                    placeholder="150"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Sačuvaj promjene
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
