import { supabase } from './supabase';
import { upsertApplicationChatPermission } from './chat-permissions';
import { Database } from './database.types';

type Campaign = Database['public']['Tables']['campaigns']['Row'];
type CampaignInsert = Database['public']['Tables']['campaigns']['Insert'];
type CampaignUpdate = Database['public']['Tables']['campaigns']['Update'];
type CampaignApplication = Database['public']['Tables']['campaign_applications']['Row'];
type CampaignApplicationInsert = Database['public']['Tables']['campaign_applications']['Insert'];

// Campaign CRUD operations
export async function createCampaign(campaign: CampaignInsert) {
  const { data, error } = await supabase
    .from('campaigns')
    .insert(campaign)
    .select()
    .single();

  return { data, error };
}

export async function getCampaign(id: string) {
  const { data, error } = await supabase
    .from('campaigns')
    .select(`
      *,
      businesses!inner(
        company_name,
        industry,
        profiles!inner(
          username,
          avatar_url
        )
      )
    `)
    .eq('id', id)
    .single();

  return { data, error };
}

export async function getCampaignWithDetails(id: string) {
  const { data, error } = await supabase
    .from('campaigns_with_details')
    .select('*')
    .eq('id', id)
    .single();

  return { data, error };
}



export async function deleteCampaign(id: string) {
  const { data, error } = await supabase
    .from('campaigns')
    .delete()
    .eq('id', id);

  return { data, error };
}

// Business campaigns
export async function getBusinessCampaigns(businessId: string, status?: string) {
  let query = supabase
    .from('campaigns')
    .select(`
      *,
      campaign_applications(count)
    `)
    .eq('business_id', businessId)
    .order('created_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
}

// Campaign search and filtering for influencers
export interface CampaignFilters {
  search?: string;
  categories?: number[];
  platforms?: number[];
  minBudget?: number;
  maxBudget?: number;
  location?: string;
  minFollowers?: number;
  maxFollowers?: number;
  gender?: string;
  deadlineBefore?: string;
  sortBy?: 'created_at' | 'budget' | 'application_deadline' | 'applications_count';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export async function searchCampaigns(filters: CampaignFilters = {}) {
  const {
    search,
    categories,
    platforms,
    minBudget,
    maxBudget,
    location,
    minFollowers,
    maxFollowers,
    gender,
    deadlineBefore,
    sortBy = 'created_at',
    sortOrder = 'desc',
    limit = 20,
    offset = 0
  } = filters;

  // Get current user for debugging
  const { data: { user } } = await supabase.auth.getUser();

  // Use RPC function to bypass RLS issues
  const { data: allCampaigns, error: rpcError } = await supabase
    .rpc('get_active_campaigns_for_influencers');

  if (rpcError) {
    console.error('RPC error:', rpcError);
    return { data: [], error: rpcError };
  }



  // Apply client-side filtering
  let filteredCampaigns = allCampaigns || [];

  // Basic text search in title and description
  if (search) {
    const searchLower = search.toLowerCase();
    filteredCampaigns = filteredCampaigns.filter(campaign =>
      campaign.title.toLowerCase().includes(searchLower) ||
      campaign.description.toLowerCase().includes(searchLower)
    );
  }

  // Budget range
  if (minBudget !== undefined) {
    filteredCampaigns = filteredCampaigns.filter(campaign => campaign.budget >= minBudget);
  }
  if (maxBudget !== undefined) {
    filteredCampaigns = filteredCampaigns.filter(campaign => campaign.budget <= maxBudget);
  }

  // Skip advanced filters for now since RPC returns basic fields only
  // TODO: Add these filters back when we have all fields in RPC

  // Sorting
  filteredCampaigns.sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'budget':
        aValue = a.budget;
        bValue = b.budget;
        break;
      default: // 'created_at'
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
    }

    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
  });

  // Pagination
  const startIndex = offset;
  const endIndex = offset + limit;
  const paginatedCampaigns = filteredCampaigns.slice(startIndex, endIndex);



  return { data: paginatedCampaigns, error: null };
}

// Get campaign for editing (only for business owners and draft campaigns)
export async function getCampaignForEdit(campaignId: string) {
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // Get campaign with all related data
  const { data: campaign, error } = await supabase
    .from('campaigns')
    .select(`
      *,
      campaign_platforms (
        platform_id,
        platforms (name)
      ),
      campaign_categories (
        category_id,
        categories (name)
      )
    `)
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .eq('status', 'draft')
    .single();

  if (error) {
    return { data: null, error };
  }

  if (!campaign) {
    return { data: null, error: { message: 'Campaign not found or not editable' } };
  }

  return { data: campaign, error: null };
}

// Update campaign (only for business owners and draft campaigns)
export async function updateCampaign(campaignId: string, campaignData: any) {
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // First check if campaign exists and is editable
  const { data: existingCampaign, error: checkError } = await supabase
    .from('campaigns')
    .select('id, status, business_id')
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .eq('status', 'draft')
    .single();

  if (checkError || !existingCampaign) {
    return { data: null, error: { message: 'Campaign not found or not editable' } };
  }

  // Update campaign
  const { data: updatedCampaign, error: updateError } = await supabase
    .from('campaigns')
    .update({
      title: campaignData.title,
      description: campaignData.description,
      budget: campaignData.budget,
      content_types: campaignData.content_types,
      min_followers: campaignData.min_followers,
      max_followers: campaignData.max_followers,
      age_range_min: campaignData.age_range_min,
      age_range_max: campaignData.age_range_max,
      gender: campaignData.gender,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .select()
    .single();

  if (updateError) {
    return { data: null, error: updateError };
  }

  // Update platforms
  if (campaignData.platforms && campaignData.platforms.length > 0) {
    // Delete existing platforms
    await supabase
      .from('campaign_platforms')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new platforms
    const platformInserts = campaignData.platforms.map((platformId: string) => ({
      campaign_id: campaignId,
      platform_id: platformId
    }));

    await supabase
      .from('campaign_platforms')
      .insert(platformInserts);
  }

  // Update categories
  if (campaignData.categories && campaignData.categories.length > 0) {
    // Delete existing categories
    await supabase
      .from('campaign_categories')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new categories
    const categoryInserts = campaignData.categories.map((categoryId: string) => ({
      campaign_id: campaignId,
      category_id: categoryId
    }));

    await supabase
      .from('campaign_categories')
      .insert(categoryInserts);
  }

  return { data: updatedCampaign, error: null };
}

// Update campaign status (with validation)
export async function updateCampaignStatus(campaignId: string, newStatus: 'draft' | 'active' | 'paused' | 'completed') {
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // First check if campaign exists and user owns it
  const { data: existingCampaign, error: checkError } = await supabase
    .from('campaigns')
    .select('id, status, business_id')
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .single();

  if (checkError || !existingCampaign) {
    return { data: null, error: { message: 'Campaign not found or access denied' } };
  }

  // Validation rules
  if (existingCampaign.status === 'active' && newStatus === 'draft') {
    return { data: null, error: { message: 'Cannot change active campaign back to draft' } };
  }

  if (existingCampaign.status === 'completed') {
    return { data: null, error: { message: 'Cannot change status of completed campaign' } };
  }

  // Update status
  const { data: updatedCampaign, error: updateError } = await supabase
    .from('campaigns')
    .update({
      status: newStatus,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .select()
    .single();

  if (updateError) {
    return { data: null, error: updateError };
  }

  return { data: updatedCampaign, error: null };
}

// Get business campaigns for dashboard with counts
export async function getBusinessCampaignsForDashboard() {
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    console.log('User not authenticated');
    return { data: [], error: { message: 'User not authenticated' } };
  }

  console.log('Loading campaigns for business:', user.id);

  const { data: campaigns, error } = await supabase
    .from('campaigns')
    .select(`
      id,
      title,
      description,
      budget,
      status,
      created_at,
      content_types,
      campaign_applications(count)
    `)
    .eq('business_id', user.id)
    .order('created_at', { ascending: false });

  console.log('Campaigns query result:', { campaigns, error });

  if (error) {
    console.error('Error loading campaigns:', error);
    return { data: [], error };
  }

  return { data: campaigns || [], error: null };
}

// Featured campaigns - for now just return latest active campaigns
export async function getFeaturedCampaigns(limit: number = 6) {
  const { data, error } = await supabase
    .from('campaigns')
    .select('*')
    .eq('status', 'active')
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Featured campaigns error:', error);
  }

  return { data, error };
}

// Campaign applications
export async function createCampaignApplication(application: CampaignApplicationInsert) {
  const { data, error } = await supabase
    .from('campaign_applications')
    .insert(application)
    .select()
    .single();

  return { data, error };
}

// Check if influencer already applied to campaign
export async function hasInfluencerApplied(campaignId: string, influencerId: string) {
  const { data, error } = await supabase
    .from('campaign_applications')
    .select('id, status, applied_at')
    .eq('campaign_id', campaignId)
    .eq('influencer_id', influencerId)
    .single();

  return { data, error };
}

export async function getCampaignApplications(campaignId: string, status?: string) {
  let query = supabase
    .from('campaign_applications')
    .select(`
      *,
      influencers!inner(
        *,
        profiles!inner(
          username,
          full_name,
          avatar_url,
          bio,
          location
        )
      )
    `)
    .eq('campaign_id', campaignId)
    .order('applied_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
}

export async function getInfluencerApplications(influencerId: string, status?: string) {
  try {
    console.log('Fetching applications for influencer:', influencerId);

    let query = supabase
      .from('campaign_applications')
      .select(`
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        delivery_timeframe,
        portfolio_links,
        additional_services,
        available_start_date,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          description,
          budget,
          status,
          business_id
        )
      `)
      .eq('influencer_id', influencerId)
      .order('applied_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data: applicationsData, error: applicationsError } = await query;

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError);
      throw applicationsError;
    }

    console.log('Applications data:', applicationsData);

    if (!applicationsData || applicationsData.length === 0) {
      return { data: [], error: null };
    }

    // Get business data separately
    const businessIds = applicationsData.map(app => app.campaigns.business_id);
    const { data: businessesData, error: businessesError } = await supabase
      .from('businesses')
      .select(`
        id,
        company_name
      `)
      .in('id', businessIds);

    if (businessesError) {
      console.error('Error fetching businesses:', businessesError);
      throw businessesError;
    }

    // Get business profiles
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, avatar_url')
      .in('id', businessIds);

    if (profilesError) {
      console.error('Error fetching business profiles:', profilesError);
    }

    console.log('Businesses data:', businessesData);
    console.log('Profiles data:', profilesData);

    // Transform data to match expected interface
    const transformedData = applicationsData.map(app => {
      const business = businessesData?.find(b => b.id === app.campaigns.business_id);
      const profile = profilesData?.find(p => p.id === app.campaigns.business_id);

      return {
        id: app.id,
        campaign_id: app.campaign_id,
        status: app.status,
        proposed_rate: app.proposed_rate,
        proposal_text: app.proposal_text,
        portfolio_links: app.portfolio_links,
        delivery_timeframe: app.delivery_timeframe,
        additional_services: app.additional_services,
        applied_at: app.applied_at,
        campaigns: {
          title: app.campaigns.title,
          description: app.campaigns.description,
          budget: app.campaigns.budget,
          status: app.campaigns.status,
          businesses: {
            company_name: business?.company_name || 'Unknown Company',
            profiles: {
              username: profile?.username || 'Unknown',
              avatar_url: profile?.avatar_url || null
            }
          }
        }
      };
    });

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error in getInfluencerApplications:', error);
    return { data: null, error: error.message };
  }
}



// Campaign platforms and categories
export async function addCampaignPlatforms(campaignId: string, platforms: Array<{
  platform_id: number;
  content_type_ids: number[];
  posts_required?: number;
  budget_per_post?: number;
}>) {
  const platformData = platforms.map(platform => ({
    campaign_id: campaignId,
    ...platform
  }));

  const { data, error } = await supabase
    .from('campaign_platforms')
    .insert(platformData)
    .select();

  return { data, error };
}

export async function addCampaignCategories(campaignId: string, categoryIds: number[]) {
  const categoryData = categoryIds.map(categoryId => ({
    campaign_id: campaignId,
    category_id: categoryId
  }));

  const { data, error } = await supabase
    .from('campaign_categories')
    .insert(categoryData)
    .select();

  return { data, error };
}

export async function removeCampaignPlatforms(campaignId: string) {
  const { data, error } = await supabase
    .from('campaign_platforms')
    .delete()
    .eq('campaign_id', campaignId);

  return { data, error };
}

export async function removeCampaignCategories(campaignId: string) {
  const { data, error } = await supabase
    .from('campaign_categories')
    .delete()
    .eq('campaign_id', campaignId);

  return { data, error };
}

// Utility functions

export async function refreshCampaignsSearchView() {
  const { data, error } = await supabase
    .rpc('refresh_campaigns_search_view');

  return { data, error };
}

// Get campaign statistics for business dashboard
export async function getCampaignStats(businessId: string) {
  const { data: campaigns, error: campaignsError } = await supabase
    .from('campaigns')
    .select('status')
    .eq('business_id', businessId);

  if (campaignsError) return { data: null, error: campaignsError };

  const { data: applications, error: applicationsError } = await supabase
    .from('campaign_applications')
    .select('status, campaign_id')
    .in('campaign_id', campaigns?.map(c => c.id) || []);

  if (applicationsError) return { data: null, error: applicationsError };

  const stats = {
    totalCampaigns: campaigns?.length || 0,
    activeCampaigns: campaigns?.filter(c => c.status === 'active').length || 0,
    completedCampaigns: campaigns?.filter(c => c.status === 'completed').length || 0,
    totalApplications: applications?.length || 0,
    pendingApplications: applications?.filter(a => a.status === 'pending').length || 0,
    acceptedApplications: applications?.filter(a => a.status === 'accepted').length || 0,
  };

  return { data: stats, error: null };
}



// Get all applications for business campaigns
export async function getBusinessCampaignApplications(businessId: string, status?: string) {
  try {
    console.log('Fetching applications for business:', businessId);

    // Use the correct field names from the actual database schema
    let query = supabase
      .from('campaign_applications')
      .select(`
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        delivery_timeframe,
        portfolio_links,
        additional_services,
        available_start_date,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          budget,
          business_id
        )
      `)
      .eq('campaigns.business_id', businessId)
      .order('applied_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data: applicationsData, error: applicationsError } = await query;

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError);
      throw applicationsError;
    }

    console.log('Applications data:', applicationsData);

    if (!applicationsData || applicationsData.length === 0) {
      return { data: [], error: null };
    }

    // Get influencer data with profiles joined
    const influencerIds = applicationsData.map(app => app.influencer_id);
    const { data: influencersData, error: influencersError } = await supabase
      .from('influencers')
      .select(`
        id,
        profiles!inner(
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .in('id', influencerIds);

    if (influencersError) {
      console.error('Error fetching influencers:', influencersError);
      throw influencersError;
    }

    console.log('Influencers data:', influencersData);

    // Transform data to match expected interface
    const transformedData = applicationsData?.map(app => {
      const influencer = influencersData?.find(inf => inf.id === app.influencer_id);
      return {
        id: app.id,
        campaign_id: app.campaign_id,
        influencer_id: app.influencer_id,
        status: app.status,
        proposed_rate: app.proposed_rate,
        proposal_text: app.proposal_text,
        delivery_timeframe: app.delivery_timeframe || 'Nije specificirano',
        portfolio_links: app.portfolio_links || [],
        experience_relevant: app.experience_relevant || app.proposal_text,
        audience_insights: app.audience_insights || '',
        additional_services: app.additional_services || '',
        available_start_date: app.available_start_date,
        applied_at: app.applied_at,
        campaigns: {
          id: app.campaigns.id,
          title: app.campaigns.title,
          budget: app.campaigns.budget,
          business_id: app.campaigns.business_id
        },
        profiles: {
          id: influencer?.profiles?.id || app.influencer_id,
          username: influencer?.profiles?.username || 'Unknown',
          full_name: influencer?.profiles?.full_name || 'Unknown User',
          avatar_url: influencer?.profiles?.avatar_url || null
        }
      };
    });

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign applications:', error);
    return { data: null, error: error.message };
  }
}

// Get single application with detailed info
export async function getCampaignApplication(applicationId: string) {
  try {
    const { data, error } = await supabase
      .from('campaign_applications')
      .select(`
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_price,
        message,
        portfolio_urls,
        estimated_delivery_days,
        additional_services,
        applied_at,
        campaigns!inner(
          id,
          title,
          description,
          budget,
          requirements,
          deliverables
        ),
        influencers!inner(
          id,
          full_name,
          username,
          avatar_url,
          bio,
          followers_count
        )
      `)
      .eq('id', applicationId)
      .single();

    if (error) throw error;

    // Get influencer categories and platforms
    const { data: categoriesData } = await supabase
      .from('influencer_categories')
      .select('categories(name)')
      .eq('influencer_id', data.influencer_id);

    const { data: platformsData } = await supabase
      .from('influencer_platforms')
      .select(`
        handle,
        followers_count,
        platforms(name)
      `)
      .eq('influencer_id', data.influencer_id);

    // Transform data
    const transformedData = {
      id: data.id,
      campaign_id: data.campaign_id,
      influencer_id: data.influencer_id,
      status: data.status,
      proposed_price: data.proposed_price,
      delivery_timeframe: `${data.estimated_delivery_days} dana`,
      portfolio_links: data.portfolio_urls || [],
      relevant_experience: data.message,
      audience_insights: data.additional_services || '',
      additional_services: [],
      created_at: data.applied_at,
      campaign: {
        id: data.campaigns.id,
        title: data.campaigns.title,
        description: data.campaigns.description,
        budget: data.campaigns.budget,
        requirements: data.campaigns.requirements,
        deliverables: data.campaigns.deliverables,
      },
      influencer: {
        id: data.influencers.id,
        full_name: data.influencers.full_name,
        username: data.influencers.username,
        avatar_url: data.influencers.avatar_url,
        bio: data.influencers.bio,
        followers_count: data.influencers.followers_count,
        categories: categoriesData?.map(c => c.categories?.name).filter(Boolean) || [],
        platforms: platformsData?.map(p => ({
          platform_name: p.platforms?.name || '',
          handle: p.handle,
          followers_count: p.followers_count,
        })) || [],
      },
    };

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign application:', error);
    return { data: null, error: error.message };
  }
}

// Update application status
export async function updateApplicationStatus(
  applicationId: string,
  status: 'accepted' | 'rejected',
  rejectionReason?: string
) {
  try {
    const updateData: any = { status };

    if (status === 'rejected' && rejectionReason) {
      updateData.rejection_reason = rejectionReason;
    }

    const { data, error } = await supabase
      .from('campaign_applications')
      .update(updateData)
      .eq('id', applicationId)
      .select(`
        *,
        campaigns!inner(business_id)
      `)
      .single();

    if (error) throw error;

    // Kreiraj chat dozvolu kada se aplikacija prihvati
    if (status === 'accepted' && data) {
      try {
        // Business je odobrio prihvatanjem aplikacije
        await upsertApplicationChatPermission(
          data.campaigns.business_id,
          data.influencer_id,
          data.id,
          true,  // business_approved - business je odobrio prihvatanjem
          false  // influencer_approved - čeka da influencer odobri
        );
      } catch (chatError) {
        console.error('Error creating chat permission:', chatError);
        // Ne prekidamo proces ako chat dozvola ne uspije
      }
    }

    return { data, error: null };
  } catch (error: any) {
    console.error('Error updating application status:', error);
    return { data: null, error: error.message };
  }
}
