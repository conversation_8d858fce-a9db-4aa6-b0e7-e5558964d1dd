'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getCampaignWithDetails, hasInfluencerApplied } from '@/lib/campaigns';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ArrowLeft,
  Loader2,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  Eye,
  MessageCircle,
  Share2,
  Clock,
  Target,
  CheckCircle,
  AlertCircle,
  Send,
  Edit
} from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';
import { CampaignApplicationForm } from '@/components/campaigns/campaign-application-form';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface CampaignDetails {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  requirements: string | null;
  deliverables: string | null;
  collaboration_type: string;
  payment_terms: string | null;
  usage_rights: string | null;
  exclusivity_period: number | null;
  revisions_included: number;
  views_count: number;
  applications_count: number;
  created_at: string;
  company_name: string;
  industry: string;
  business_username: string;
  business_avatar: string | null;
  platforms: Array<{
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    content_type_ids: number[];
    posts_required: number;
    budget_per_post: number;
  }>;
  categories: Array<{
    category_id: number;
    category_name: string;
    category_icon: string;
  }>;
}

export default function CampaignDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [applicationStatus, setApplicationStatus] = useState<{
    hasApplied: boolean;
    status?: string;
    appliedAt?: string;
  }>({ hasApplied: false });

  const campaignId = params.id as string;

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user && campaignId) {
      loadCampaign();
    }
  }, [user, authLoading, campaignId, router]);

  const loadCampaign = async () => {
    try {
      setLoading(true);

      const { data, error } = await getCampaignWithDetails(campaignId);
      
      if (error) {
        setError('Greška pri učitavanju kampanje');
        return;
      }

      if (!data) {
        setError('Kampanja nije pronađena');
        return;
      }

      setCampaign(data);

      // Check if current user is the owner of the campaign
      setIsOwner(data.business_id === user?.id);

      // Check if influencer already applied (only for non-owners)
      if (data.business_id !== user?.id && user?.id) {
        const { data: applicationData } = await hasInfluencerApplied(campaignId, user.id);
        if (applicationData) {
          setApplicationStatus({
            hasApplied: true,
            status: applicationData.status,
            appliedAt: applicationData.applied_at
          });
        }
      }
    } catch (err) {
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle>Greška</CardTitle>
            <CardDescription>{error || 'Kampanja nije pronađena'}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => router.back()}
            >
              Nazad
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: 'Nacrt', variant: 'secondary' as const },
      active: { label: 'Aktivna', variant: 'default' as const },
      paused: { label: 'Pauzirana', variant: 'outline' as const },
      completed: { label: 'Završena', variant: 'secondary' as const },
      cancelled: { label: 'Otkazana', variant: 'destructive' as const },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.draft;
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const getCollaborationTypeLabel = (type: string) => {
    const typeMap = {
      paid: 'Plaćena saradnja',
      barter: 'Barter (razmena)',
      hybrid: 'Hibridna saradnja'
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb */}
      <div className="border-b bg-muted/30">
        <div className="container mx-auto px-4 py-3">
          <nav className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link href="/dashboard/biznis" className="hover:text-foreground transition-colors">
              Dashboard
            </Link>
            <span>/</span>
            <Link href="/dashboard/campaigns" className="hover:text-foreground transition-colors">
              Kampanje
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">{campaign.title}</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Header */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                    {campaign.title}
                  </h1>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      {campaign.views_count} pregleda
                    </span>
                    <span className="flex items-center gap-1">
                      <MessageCircle className="h-4 w-4" />
                      {campaign.applications_count} aplikacija
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatDistanceToNow(new Date(campaign.created_at), { 
                        addSuffix: true, 
                        locale: bs 
                      })}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(campaign.status)}
                  <Button variant="outline" size="sm">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Opis kampanje</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                  {campaign.description}
                </p>
              </CardContent>
            </Card>

            {/* Platforms and Categories */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Platforme</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {campaign.platforms.map((platform) => (
                      <div key={platform.platform_id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{platform.platform_icon}</span>
                          <span className="font-medium">{platform.platform_name}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {platform.posts_required} objava
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Kategorije</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {campaign.categories.map((category) => (
                      <Badge key={category.category_id} variant="secondary" className="flex items-center gap-1">
                        <span>{category.category_icon}</span>
                        {category.category_name}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Tipovi sadržaja</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {campaign.content_types?.map((contentType) => (
                      <Badge key={contentType} variant="outline" className="capitalize">
                        {contentType}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Requirements and Deliverables */}
            {(campaign.requirements || campaign.deliverables) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {campaign.requirements && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Zahtevi</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {campaign.requirements}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {campaign.deliverables && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Očekivani rezultati</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {campaign.deliverables}
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Business Info */}
            <Card>
              <CardHeader>
                <CardTitle>Biznis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 mb-4">
                  <Avatar>
                    <AvatarImage src={campaign.business_avatar || undefined} />
                    <AvatarFallback>
                      {campaign.company_name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{campaign.company_name}</h3>
                    <p className="text-sm text-muted-foreground">@{campaign.business_username}</p>
                  </div>
                </div>
                {campaign.industry && (
                  <p className="text-sm text-muted-foreground">
                    Industrija: {campaign.industry}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Campaign Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji kampanje</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Budžet</span>
                  <span className="font-medium flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    {campaign.budget.toLocaleString()} KM
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Tip saradnje</span>
                  <span className="font-medium">
                    {getCollaborationTypeLabel(campaign.collaboration_type)}
                  </span>
                </div>

                {campaign.location && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Lokacija</span>
                    <span className="font-medium flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {campaign.location}
                    </span>
                  </div>
                )}

                {campaign.application_deadline && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Rok za prijave</span>
                    <span className="font-medium flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {new Date(campaign.application_deadline).toLocaleDateString('bs-BA')}
                    </span>
                  </div>
                )}

                {(campaign.min_followers || campaign.max_followers) && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Broj pratilaca</span>
                    <span className="font-medium flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {campaign.min_followers?.toLocaleString() || '0'} - {campaign.max_followers?.toLocaleString() || '∞'}
                    </span>
                  </div>
                )}

                {(campaign.age_range_min || campaign.age_range_max) && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Uzrast ciljne grupe</span>
                    <span className="font-medium">
                      {campaign.age_range_min || '13'} - {campaign.age_range_max || '65'} godina
                    </span>
                  </div>
                )}

                {campaign.gender && campaign.gender !== 'all' && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Pol ciljne grupe</span>
                    <span className="font-medium">
                      {campaign.gender === 'male' ? 'Muški' : 'Ženski'}
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Revizije uključene</span>
                  <span className="font-medium">{campaign.revisions_included}</span>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons - Different for Owner vs Influencer */}
            <div className="space-y-3">
              {isOwner ? (
                // Owner (Business) View
                <>
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={() => router.push(`/dashboard/biznis/applications?campaign=${campaign.id}`)}
                  >
                    <Users className="mr-2 h-5 w-5" />
                    Pregled aplikacija ({campaign.applications_count})
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    size="lg"
                    onClick={() => router.push(`/campaigns/${campaign.id}/edit`)}
                  >
                    <Edit className="mr-2 h-5 w-5" />
                    Uredi kampanju
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    size="lg"
                    onClick={() => router.push('/dashboard/biznis')}
                  >
                    <ArrowLeft className="mr-2 h-5 w-5" />
                    Nazad na dashboard
                  </Button>
                </>
              ) : (
                // Influencer View
                <>
                  {applicationStatus.hasApplied ? (
                    // Show application status
                    <Card className="w-full">
                      <CardContent className="pt-6">
                        <div className="flex items-center gap-3 mb-4">
                          <CheckCircle className="h-6 w-6 text-green-500" />
                          <div>
                            <h3 className="font-semibold">Aplikacija poslana</h3>
                            <p className="text-sm text-muted-foreground">
                              Prijavili ste se na ovu kampanju {applicationStatus.appliedAt && formatDistanceToNow(new Date(applicationStatus.appliedAt), { addSuffix: true, locale: bs })}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            applicationStatus.status === 'accepted' ? 'default' :
                            applicationStatus.status === 'rejected' ? 'destructive' : 'secondary'
                          }>
                            {applicationStatus.status === 'pending' && 'Čeka se odgovor'}
                            {applicationStatus.status === 'accepted' && 'Prihvaćeno'}
                            {applicationStatus.status === 'rejected' && 'Odbačeno'}
                          </Badge>
                        </div>
                        {applicationStatus.status === 'pending' && (
                          <p className="text-sm text-muted-foreground mt-2">
                            Biznis će uskoro pregledati vašu aplikaciju i odgovoriti.
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    // Show application form
                    <Dialog open={showApplicationForm} onOpenChange={setShowApplicationForm}>
                      <DialogTrigger asChild>
                        <Button className="w-full" size="lg">
                          <Send className="mr-2 h-5 w-5" />
                          Pošaljite ponudu
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Aplikacija za kampanju</DialogTitle>
                          <DialogDescription>
                            Pošaljite vašu ponudu za kampanju "{campaign.title}"
                          </DialogDescription>
                        </DialogHeader>
                        <CampaignApplicationForm
                          campaign={{
                            id: campaign.id,
                            title: campaign.title,
                            budget: campaign.budget,
                            description: campaign.description,
                            company_name: campaign.company_name,
                            platforms: campaign.platforms
                          }}
                          onSuccess={() => {
                            setShowApplicationForm(false);
                            // Reload to show application status
                            loadCampaign();
                          }}
                          onCancel={() => setShowApplicationForm(false)}
                        />
                      </DialogContent>
                    </Dialog>
                  )}
                  <Button variant="outline" className="w-full" size="lg">
                    <MessageCircle className="mr-2 h-5 w-5" />
                    Kontaktiraj biznis
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
