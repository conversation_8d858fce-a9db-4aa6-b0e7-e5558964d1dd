import { supabase } from '@/lib/supabase';

export interface ChatPermission {
  id: string;
  business_id: string;
  influencer_id: string;
  offer_id: string | null;
  campaign_application_id: string | null;
  business_approved: boolean | null;
  influencer_approved: boolean | null;
  chat_enabled: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

/**
 * Create or update chat permission for direct offer
 */
export async function upsertOfferChatPermission(
  businessId: string,
  influencerId: string,
  offerId: string,
  businessApproved: boolean = false,
  influencerApproved: boolean = false
) {
  const { data, error } = await supabase.rpc('upsert_chat_permission', {
    p_business_id: businessId,
    p_influencer_id: influencerId,
    p_offer_id: offerId,
    p_business_approved: businessApproved,
    p_influencer_approved: influencerApproved
  });

  if (error) {
    console.error('Error upserting offer chat permission:', error);
    throw error;
  }

  return data;
}

/**
 * Create or update chat permission for campaign application
 */
export async function upsertApplicationChatPermission(
  businessId: string,
  influencerId: string,
  applicationId: string,
  businessApproved: boolean = false,
  influencerApproved: boolean = false
) {
  const { data, error } = await supabase.rpc('upsert_chat_permission', {
    p_business_id: businessId,
    p_influencer_id: influencerId,
    p_campaign_application_id: applicationId,
    p_business_approved: businessApproved,
    p_influencer_approved: influencerApproved
  });

  if (error) {
    console.error('Error upserting application chat permission:', error);
    throw error;
  }

  return data;
}

/**
 * Check if chat is enabled between business and influencer for specific offer/application
 */
export async function isChatEnabled(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
): Promise<boolean> {
  let query = supabase
    .from('chat_permissions')
    .select('chat_enabled')
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { data, error } = await query.single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No permission record found, chat not enabled
      return false;
    }
    console.error('Error checking chat permission:', error);
    throw error;
  }

  return data?.chat_enabled || false;
}

/**
 * Get chat permission details
 */
export async function getChatPermission(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
): Promise<ChatPermission | null> {
  let query = supabase
    .from('chat_permissions')
    .select('*')
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { data, error } = await query.single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No permission record found
      return null;
    }
    console.error('Error getting chat permission:', error);
    throw error;
  }

  return data;
}

/**
 * Approve chat from business side
 */
export async function approveBusinessChat(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
) {
  let query = supabase
    .from('chat_permissions')
    .update({ business_approved: true })
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { error } = await query;

  if (error) {
    console.error('Error approving business chat:', error);
    throw error;
  }
}

/**
 * Approve chat from influencer side
 */
export async function approveInfluencerChat(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
) {
  let query = supabase
    .from('chat_permissions')
    .update({ influencer_approved: true })
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { error } = await query;

  if (error) {
    console.error('Error approving influencer chat:', error);
    throw error;
  }
}

/**
 * Get all chat permissions for a user (business or influencer)
 */
export async function getUserChatPermissions(userId: string): Promise<ChatPermission[]> {
  const { data, error } = await supabase
    .from('chat_permissions')
    .select('*')
    .or(`business_id.eq.${userId},influencer_id.eq.${userId}`)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error getting user chat permissions:', error);
    throw error;
  }

  return data || [];
}
